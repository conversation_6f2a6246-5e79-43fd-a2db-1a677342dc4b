'use client';

import React, { useEffect, useState } from 'react';
import { fallback_image } from '@/config/constants';
import { parseWithBR } from '@/lib/utils';

const BlogsCards = () => {
  const [blogs, setBlogs] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const course = 'PROFESSIONAL';
  const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        let url = `${baseUrl}/z/blogs/pages/?type=blogs.BlogPage&limit=1000&fields=banner_image,description,category`;
        if (course) {
          url += `,course_group&course_group=${course}`;
        }

        const response = await fetch(url);

        if (!response.ok) {
          throw new Error('Failed to fetch blogs');
        }
        const data = await response.json();
        setBlogs(data.items || []);
      } catch (err: any) {
        setError(err.message || 'An error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchBlogs();
  }, [baseUrl, course]);

  if (isLoading) {
    return (
      <div className="px-[20px] py-8 lg:py-16 lg:pb-8 lg:px-[100px] lg:pt-20">
        Loading...
      </div>
    );
  }

  if (error) {
    return (
      <div className="px-[20px] py-8 lg:py-16 lg:pb-8 lg:px-[100px] lg:pt-20 text-red-600">
        Error: {error}
      </div>
    );
  }

  if (!blogs || blogs.length === 0) {
    return (
      <section className="px-[20px] py-8 lg:py-16 lg:pb-8 lg:px-[100px] lg:pt-20">
        <h3 className="block font-primary_medium text-2xl md:text-3xl lg:text-4xl my-4">
          Blogs
        </h3>
        <div className="flex flex-col items-center justify-center min-h-[200px] bg-gray-50 rounded-lg">
          <div className="text-gray-500 text-lg font-medium">
            No blogs available yet
          </div>
          <p className="text-gray-400 text-sm mt-2">
            Check back later for updates
          </p>
        </div>
      </section>
    );
  }

  return (
    <div className="px-[20px] py-8 lg:py-16 lg:pb-8 lg:px-[100px] lg:pt-20">
      <h3 className="block font-primary_medium text-2xl md:text-3xl lg:text-4xl my-4">
        Blogs
      </h3>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        {blogs.map((blog: any) => (
          <BlogCard key={blog.id} blog={blog} />
        ))}
      </div>
    </div>
  );
};

const BlogCard = ({ blog }: { blog: any }) => {
  return (
    <div className="bg-white w-full h-[600px] rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 ease-in-out flex flex-col relative">
      {/* Image container with fixed aspect ratio */}
      <div className="w-full aspect-video">
        <img
          src={blog.banner_image?.meta.download_url || fallback_image}
          alt={blog.title || 'Blog Banner'}
          className="w-full object-contain max-h-[350px] bg-gray-100"
        />
      </div>

      {/* Content container */}
      <div className="p-4 flex-grow">
        {/* Title */}
        <h4 className="text-lg font-semibold text-gray-900 line-clamp-2 mb-3">
          {blog.title}
        </h4>

        {/* Description with padding bottom to make space for the read more link */}
        <div className="overflow-hidden pb-12">
          <p className="text-gray-600 text-sm leading-relaxed line-clamp-5">
            {parseWithBR(blog.description)}
          </p>
        </div>
      </div>

      {/* Read more link positioned absolutely at the bottom */}
      <div className="absolute bottom-4 left-4 right-4">
        <a
          href={`blogs/${blog.meta.slug}`}
          className="text-indigo-600 hover:text-indigo-800 text-sm font-medium transition duration-200 block"
        >
          Read more
        </a>
      </div>
    </div>
  );
};

export default BlogsCards;
