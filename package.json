{"name": "gradehunt_desktop", "private": true, "version": "1.0.1", "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start -H 0.0.0.0 -p {PORT:-8080}", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "generate-sitemap": "node generate-sitemap.js"}, "reactSnap": {"puppeteerExecutablePath": "/usr/bin/chromium", "puppeteerArgs": ["--no-sandbox"], "source": "dist"}, "dependencies": {"@next/bundle-analyzer": "^15.0.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-checkbox": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@reduxjs/toolkit": "^2.2.5", "@sentry/nextjs": "^8.55.0", "@tailwindcss/forms": "^0.5.7", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "firebase": "^10.12.3", "framer-motion": "^11.3.29", "html-react-parser": "^5.2.2", "lodash": "^4.17.21", "lucide-react": "^0.396.0", "next": "^14.2.15", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.2.1", "react-modal": "^3.16.1", "react-razorpay": "^2.0.1", "react-redux": "^9.1.2", "react-router-dom": "^6.27.0", "react-spinners": "^0.14.1", "redux-persist": "^6.0.0", "sitemap": "^8.0.0", "swiper": "^11.2.6", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@next/eslint-plugin-next": "^15.0.1", "@types/lodash": "^4.17.7", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@types/react-helmet": "^6.1.11", "@types/react-modal": "^3.16.3", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.14", "postcss": "^8.4.38", "rollup-plugin-visualizer": "^5.12.0", "serve": "^14.2.3", "tailwindcss": "^3.4.4", "typescript": "^5.2.2"}}