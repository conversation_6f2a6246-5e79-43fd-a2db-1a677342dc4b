'use client';

import TestSeriesButton from '@/components/ui/TestSeriesButton';
import { FaArrowDown } from 'react-icons/fa6';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, persistor } from '@/redux/store';
import { useEffect, useState } from 'react';
import ClipLoader from 'react-spinners/ClipLoader';
import { useRouter } from 'next/navigation';
import { baseUrl, digital_ocean_base_url } from '@/config/constants';
import {
  useCreateSubmissionMutation,
  useGetSubmissionsQuery,
} from '@/redux/apiSlice';
import toast from 'react-hot-toast';
import {
  setSelectedTestPaperStatus,
  updateTestPaperStatus,
} from '@/redux/features/testPaperSlice';
import axios from 'axios';
import DoubtModal from './doubt_modal';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import SecondaryButton from '../ui/SecondaryButton';
import { File } from 'lucide-react';
import { logoutUser, updateIdToken } from '@/redux/features/userSlice';
import { auth } from '@/data/firebase';

interface doubt {
  doubt_status: boolean;
  answer: string;
  question: string;
  created_at: string;
  attached_object: string;
}

interface TestSeriesType {
  id: number;
  title: string;
  description: string;
  created_at: string;
  updated_at: string;
}

interface Subject {
  id: number;
  title: string;
  description: string;
  course_type: number;
}

interface Attempt {
  id: number;
  month: string;
  year: number;
}

interface CourseType {
  id: number;
  title: string;
  description: string;
  subjects: Subject[];
  attempts: Attempt[];
}

interface FeesStructureAndSyllabus {
  id: number;
  fees_structure: string;
  syllabus: string;
  created_at: string;
  test_series_type: number;
  course_type: number;
  attempts: number[];
}

interface TestSeries {
  id: number;
  title: string;
  description: string;
  test_series_type: TestSeriesType;
  course_type: CourseType;
  test_series_attempts: Attempt[];
  status: boolean;
  discounted_price: number;
  mrp_price: number;
  fees_structure_and_syllabus: FeesStructureAndSyllabus;
  valid_from: string;
  valid_till: string;
  created_at: string;
  updated_at: string;
}

interface SubjectInfo {
  id: number;
  title: string;
  description: string;
  course_type: number;
  valid_till: string;
}

interface Test {
  id: number;
  test_title: string;
  test_series: TestSeries;
  valid_from: string;
  valid_to: string;
  subject: SubjectInfo;
  time_duration: number;
  total_marks: number;
  question_paper: string;
  answer_key: string;
  created_at: string;
}

interface User {
  id: number;
  phone: string;
  email: string;
  name: string;
  gender: string;
  firebase_uid: string;
}

interface AssignedTeacher {
  id: number;
  first_name: string;
  last_name: string;
  phone_number: string;
  email: string | null;
  role: string;
}

interface Paper {
  id: number;
  test: Test;
  user: User;
  assigned_teacher: AssignedTeacher;
  remarks: string;
  submitted_answer_sheet: string;
  checked_answer_sheet: string;
  check_status: boolean;
  created_at: string;
}

interface error {
  detail: string;
}
interface QuesPaperSectionProps {
  paperId: string; // or number, depending on the data type you expect
}

const QuesPaperSection: React.FC<QuesPaperSectionProps> = ({ paperId }) => {
  const router = useRouter();
  const currentUser = useSelector((state: RootState) => state.user);
  const currentTestPaper = useSelector(
    (state: RootState) => state.testPaper.selectedTestPaper
  );
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [spinnerColor] = useState('#30b954');
  const [headerToken, setHeaderToken] = useState('');

  useEffect(() => {
    const fetchToken = async () => {
      auth.onAuthStateChanged(async (user) => {
        if (user) {
          try {
            const token = await user.getIdToken(true);
            setHeaderToken(token);
            dispatch(updateIdToken(token));
          } catch (error) {
            console.log('token');
            console.error('Error fetching ID token:', error);
          }
        }
      });
    };

    fetchToken();
  }, []);

  const { data: allSubmissionsData, refetch } = useGetSubmissionsQuery(
    { headerToken: headerToken || currentUser.tokenResponse.idToken },
    { skip: !headerToken }
  );
  const [createSubmission] = useCreateSubmissionMutation();
  const user = useSelector((state: RootState) => state.user);
  const [doubts, setDoubts] = useState<doubt[]>();
  const [paper, setPaper] = useState<Paper | null>(null);
  const [error, setError] = useState<error | null>(null);
  const dispatch = useDispatch();
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] =
    useState<boolean>(false);

  const checkExpiry = () => {
    const validTo =
      paper?.test.subject.valid_till ||
      currentTestPaper?.test_paper?.valid_to ||
      '';
    const [day, month, year] = validTo.split('/').map(Number);
    const formattedDate = new Date(parseInt(`20${year}`), month - 1, day); // Assuming it's in the format 20YY
    const currentDate = new Date();
    return formattedDate < currentDate;
  };

  const checkValidity = () => {
    const validFrom =
      paper?.test.valid_from || currentTestPaper?.test_paper?.valid_from || '';
    const [day, month, year] = validFrom.split('/').map(Number);
    const formattedDate = new Date(parseInt(`20${year}`), month - 1, day); // Assuming it's in the format 20YY
    const currentDate = new Date();
    return formattedDate < currentDate;
  };

  useEffect(() => {
    console.log('Updated user:', user);
    console.log('Updated test paper:', currentTestPaper);
  }, [user, currentTestPaper]);

  function convertTimestamp(timestamp: string | number | Date) {
    const date = new Date(timestamp);

    // Format the day, month, and year
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-based
    const year = String(date.getFullYear()).slice(-2);

    // Convert to IST (Indian Standard Time) by offsetting 5 hours 30 minutes
    const istDate = new Date(date.getTime() + 5.5 * 60 * 60 * 1000);

    // Get hours, minutes in IST
    let hours = istDate.getUTCHours();
    const minutes = String(istDate.getUTCMinutes()).padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';

    hours = hours % 12 || 12; // Convert to 12-hour format

    return `${day}/${month}/${year} ${hours}:${minutes} ${ampm} IST`;
  }

  const getDoubts = async () => {
    const firebaseUid = user.firebaseUID;
    const urlResponse = await fetch(
      `${baseUrl}/doubt/?test_paper=${currentTestPaper?.test_paper?.id}`,
      {
        method: 'GET',
        headers: {
          Authorization: `${headerToken || currentUser.tokenResponse.idToken}`,
          UID: `${firebaseUid}`,
        },
      }
    );
    const urlData = await urlResponse.json();
    setDoubts(urlData);
  };

  useEffect(() => {
    getDoubts();
  }, [currentTestPaper?.test_paper, paper, headerToken]);

  const getSubmission = async () => {
    try {
      const response = await axios.get(
        `${baseUrl}/submissions-by-test/?test_id=${paperId}`,
        {
          headers: {
            Authorization: `${
              headerToken || currentUser.tokenResponse.idToken
            }`,
          },
        }
      );
      setPaper(response.data[0]);
      setError({ detail: '' });
    } catch (error: any) {
      console.error('Error fetching the submission:', error.response.data);
      if (error.response.status == 403 && headerToken) {
        dispatch(logoutUser());
        persistor.purge();
        router.push('/auth/login');
      }
      setError(error.response.data);
    }
  };
  useEffect(() => {
    getSubmission();
  }, [headerToken]);

  useEffect(() => {
    if (error?.detail === 'No submissions found for this test and user.') {
      const paperIdAsNumber = Array.isArray(paperId)
        ? parseInt(paperId[0], 10)
        : parseInt(paperId, 10);

      if (paperIdAsNumber === currentTestPaper?.test_paper?.id) {
        setError({ detail: 'Not Submitted' });
      } else {
        setError({ detail: 'Not Bought' });
      }
    }

    if (error?.detail === 'Not Bought') {
      router.push('/purchased/tests');
    }
  }, [paperId, error, currentTestPaper, router]);

  // useEffect(() => {
  //   refetch();
  // }, [refetch]);

  useEffect(() => {
    if (!currentTestPaper || currentTestPaper === undefined) {
      router.push('/');
    }
    console.log(currentTestPaper);
  }, [currentTestPaper]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      const file = event.target.files[0];
      if (file.type !== 'application/pdf') {
        alert('Only PDF files are allowed.');
        return;
      }
      setSelectedFile(file);
    }
  };

  const handleCreatePreSignedURL = async () => {
    setIsConfirmationModalOpen(false);
    if (!selectedFile) {
      toast('No file selected');
      return;
    }
    // const isConfirmed = window.confirm("Are you sure you want to submit? Submission can only be done once, and it can be edited later.");
    // if (!isConfirmed) {
    //     return; // If the user cancels, return early and do nothing
    // }

    const currentTestPaperId = currentTestPaper?.test_paper?.id;

    if (
      allSubmissionsData?.some(
        (submission) =>
          submission.test.id == currentTestPaperId &&
          submission.user.id == currentUser.userId
      )
    ) {
      toast('Already Answer Paper Submitted!');
      return;
    }

    setIsSubmitting(true);
    const studentIdentifier =
      currentUser.name?.replace(/\s+/g, '-') || currentUser.userId;
    const testTitle =
      currentTestPaper?.test_paper?.test_title?.replace(/\s+/g, '-') || 'test';
    const timestamp = Date.now();
    const fileExtension = selectedFile.name.split('.').pop() || '';

    const fileObjectName = `${studentIdentifier}-${testTitle}-${timestamp}.${fileExtension}`;

    try {
      const urlResponse = await fetch(
        `${baseUrl}/submission-presigned-url/?object_key=${fileObjectName}`,
        {
          method: 'GET',
          headers: {
            Authorization: `${
              headerToken || currentUser.tokenResponse.idToken
            }`,
          },
        }
      );
      const urlData = await urlResponse.json();

      if (urlData) {
        const preSignedUrl = urlData.url;

        // Upload the file content to the pre-signed URL
        const uploadResponse = await fetch(preSignedUrl, {
          method: 'PUT',
          headers: {
            'Content-Type': selectedFile.type,
            'x-amz-acl': 'public-read',
          },
          body: selectedFile,
        });

        if (uploadResponse.status == 200 || uploadResponse.status == 201) {
          const bodyData = {
            test: currentTestPaper?.test_paper?.id,
            user: currentUser.userId,
            submitted_answer_sheet: `${digital_ocean_base_url}/submissions/${fileObjectName}`,
          };
          const { data: res } = await createSubmission({
            headerToken: headerToken || currentUser.tokenResponse.idToken,
            body: bodyData,
          });

          if (res) {
            toast('Answer Paper Submitted Successfully');
            getSubmission();
            dispatch(
              updateTestPaperStatus({
                id: currentTestPaper?.test_paper?.id || -1,
                status: 'Processing',
              })
            );
            dispatch(
              setSelectedTestPaperStatus({
                id: currentTestPaper?.test_paper?.id || -1,
                status: 'Processing',
              })
            );
            refetch();
            return;
          }
        } else {
          console.error('Failed to upload file', uploadResponse.statusText);
        }
      }
    } catch (error) {
      console.error('An error occurred', error);
    } finally {
      setSelectedFile(null);
      setIsSubmitting(false);
    }
  };

  const resetFileInput = () => {
    const fileInput = document.getElementById(
      'file-upload'
    ) as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
    setSelectedFile(null);
  };

  return (
    <div className="w-full">
      <Dialog
        open={isConfirmationModalOpen}
        onOpenChange={() => setIsConfirmationModalOpen(false)}
      >
        <DialogContent className="sm:max-w-sm" aria-describedby="">
          <DialogHeader>
            <DialogTitle className="font-primary_medium text-[#181818] font-medium text-[22px] leading-7">
              Confirmation For Submission
            </DialogTitle>
          </DialogHeader>

          <div className="mx-auto w-full">
            Are you sure you want to submit? Submission can only be done once.
          </div>

          <DialogFooter className="">
            <div onClick={handleCreatePreSignedURL} className="w-full">
              <SecondaryButton additionalClassNames="w-full my-2">
                Confirm
              </SecondaryButton>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <div className="flex lg:px-6 lg:py-2 rounded-lg lg:border-none border-b-[1px] border-[#D9D9D9] justify-between items-center bg-none lg:bg-[#CFE0FF] min-h-[68px]">
        <div className="flex gap-4 items-center">
          <img
            loading="lazy"
            className="cursor-pointer"
            onClick={() => router.push('/purchased/tests')}
            src="/icons/back.svg"
            alt="back"
          />
          <div className="flex flex-col items-start">
            <p className="text-[#1D1D1D] font-primary_medium text-sm md:text-base lg:text-[22px] leading-7 ">
              {paper?.test?.subject?.title || currentTestPaper?.subject.title}
            </p>
            <p className="text-[#3F3F3F] font-primary text-sm md:text-base leading-5 ">
              {paper?.test?.test_series.test_series_type.title ||
                currentTestPaper?.test_series.test_series_type.title}
            </p>
          </div>
        </div>
        <div className="flex flex-col items-start text-left">
          <p className="text-[#1D1D1D] font-primary text-xs md:text-sm lg:text-base leading-5 text-left">
            Valid Till
          </p>
          <p className="text-[#3F3F3F] font-primary_medium text-[12px] lg:text-base leading-5 text-left">
            {paper?.test.subject.valid_till ||
              currentTestPaper?.test_paper?.valid_to ||
              ''}
          </p>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row mt-4 items-start">
        {checkValidity() ? (
          <div className="w-full lg:w-[70%] bg-[#EEEEEE] mt-7 p-6 rounded-lg flex flex-col max-h-[80vh] gap-4 justify-between overflow-hidden">
            <h2 className="text-[#000000] font-primary_medium text-lg md:text-2xl leading-7">
              {paper?.test.test_title ||
                currentTestPaper?.test_paper?.test_title}
            </h2>

            <div className="w-full flex justify-between items-center border-b-[1px] border-[#CAC4D0] pb-2">
              <p className="text-[#000000] font-primary text-base leading-5 ">
                Question Paper
              </p>
              <div>
                <a
                  onClick={(e) => {
                    if (
                      paper?.test?.question_paper?.length ||
                      !currentTestPaper?.test_paper?.question_paper?.length
                    ) {
                      e.preventDefault(); // Prevent the default action
                      toast('Question Paper Not Uploaded By Gradehunt');
                    }
                  }}
                  href={
                    (paper?.test?.question_paper ||
                      currentTestPaper?.test_paper?.question_paper) ??
                    ''
                  }
                  download={
                    currentTestPaper?.test_paper?.question_paper
                      ? currentTestPaper?.test_paper?.question_paper?.length > 0
                        ? ''
                        : undefined
                      : undefined
                  }
                  target="__blank"
                >
                  <TestSeriesButton content="Download" icon={<FaArrowDown />} />
                </a>
              </div>
            </div>

            {isSubmitting ? (
              <div className="min-h-[350px] flex justify-center items-center">
                <ClipLoader
                  color={spinnerColor}
                  loading={isSubmitting}
                  size={150}
                  aria-label="Loading Spinner"
                  data-testid="loader"
                />
              </div>
            ) : (
              error?.detail === 'Not Submitted' &&
              !checkExpiry() && (
                <div className="mt-5 border-b-[1px] border-b-[#CAC4D0] pb-4">
                  <p className="text-[#000000] font-primary text-base leading-5 ">
                    Answer Paper
                  </p>

                  <div className="mt-2 bg-[#FFF3DF] border-[#FFAE1E] border-dashed min-h-[30vh] border-[1px] py-4 flex items-center justify-center rounded-lg gap-3 flex-col">
                    {!checkExpiry() && (
                      <p className="text-[#3F3F3F] text-center font-primary_medium text-sm leading-5">
                        Please upload your answer paper
                      </p>
                    )}
                    <input
                      type="file"
                      accept="application/pdf"
                      className="hidden"
                      id="file-upload"
                      onChange={handleFileChange}
                    />
                    {!checkExpiry() ? (
                      !selectedFile && (
                        <label
                          htmlFor="file-upload"
                          className={`sm:h-[63px] h-auto w-auto sm:w-[174px] border-[1px] sm:py-2 sm:px-4 border-[#FFAE1E] px-8 rounded-[100px] bg-[#FFFFFF] hover:text-[#FFFFFF] hover:border-[#FFFFFF] hover:bg-[#FFAE1E] transition-all duration-150 text-[#FFAE1E] flex items-center justify-center leading-7 text-sm sm:text-[22px] font-primary_medium text-center cursor-pointer`}
                        >
                          Browse
                        </label>
                      )
                    ) : (
                      <p className="text-red-600">
                        The validity for this test has expired
                      </p>
                    )}
                    {selectedFile && (
                      <div className="flex w-full px-8 mx-auto gap-0 justify-center">
                        <p className="text-[#3F3F3F] bg-[#EEEEEE] overflow-x-auto max-w-11/12 border-[#3F3F3F] border-[1px] text-center font-primary_medium text-sm lg:text-base leading-5 p-2 sm:p-4 rounded-l-lg flex items-center gap-2 lg:mx-0 ">
                          <img loading="lazy" src="/icons/file_icon.svg" />{' '}
                          {selectedFile.name}
                        </p>
                        <button
                          onClick={resetFileInput}
                          className="bg-[#EEEEEE] text-[#3F3F3F] lg:text-base text-sm font-semibold text-center w-fit p-4 border-[#3F3F3F] border-[1px] rounded-r-lg border-l-0"
                        >
                          X
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              )
            )}

            {error?.detail === 'Not Submitted' &&
              !isSubmitting &&
              !checkExpiry() && (
                <div>
                  <button
                    className="my-4 mx-auto h-auto w-[50%] border-[1px] sm:py-2 lg:px-4 border-[#FFAE1E] px-8 rounded-[100px] bg-[#FFFFFF] hover:text-[#FFFFFF] hover:border-[#FFFFFF] hover:bg-[#FFAE1E] transition-all duration-150 text-[#FFAE1E] flex items-center justify-center leading-7 text-sm lg:text-[22px] font-primary_medium text-center cursor-pointer"
                    onClick={() => setIsConfirmationModalOpen(true)}
                    disabled={isSubmitting}
                  >
                    Submit
                  </button>
                </div>
              )}

            {!error?.detail &&
              (paper?.check_status ||
                currentTestPaper?.test_paper?.status == 'Checked') && (
                <div className="w-full flex flex-col mt-2">
                  <p className="text-[#000000] mb-4 font-primary text-base leading-5">
                    Result
                  </p>
                  <div className="grid md:grid-cols-2 w-full gap-4">
                    {/* Result Button */}
                    <div>
                      <a
                        href={paper?.checked_answer_sheet ?? ''}
                        target="_blank"
                        className="w-[50%]"
                        onClick={(e) => {
                          if (!paper?.checked_answer_sheet.length) {
                            e.preventDefault(); // Prevent the default action
                            toast('Result File Not Yet Available');
                          }
                        }}
                        download={
                          paper?.checked_answer_sheet
                            ? paper?.checked_answer_sheet?.length > 0
                              ? ''
                              : undefined
                            : undefined
                        }
                      >
                        <button className="w-[100%] hover:bg-[#3F3F3F] transition-all duration-150 hover:text-[#FFFFFF] text-[#3F3F3F] text-sm lg:text-base items-center justify-center leading-5 font-primary_medium flex rounded-[100px] h-[40px] lg:h-[48px] border-[#3F3F3F] border-[1px]">
                          Result <FaArrowDown className="ml-2" />
                        </button>
                      </a>
                    </div>

                    {/* Suggested Answer Button */}
                    <div>
                      <a
                        href={currentTestPaper?.test_paper?.answer_key ?? ''}
                        target="_blank"
                        className="w-[50%]"
                        onClick={(e) => {
                          if (
                            !currentTestPaper?.test_paper?.answer_key?.length
                          ) {
                            e.preventDefault(); // Prevent the default action
                            toast('Answer Key Not Yet Available');
                          }
                        }}
                        download={
                          currentTestPaper?.test_paper?.answer_key
                            ? currentTestPaper?.test_paper?.answer_key?.length >
                              0
                              ? ''
                              : undefined
                            : undefined
                        }
                      >
                        <button className="w-full hover:bg-[#3F3F3F] transition-all duration-150 hover:text-[#FFFFFF] text-[#3F3F3F] text-sm lg:text-base items-center justify-center leading-5 font-primary_medium flex rounded-[100px] h-[40px] lg:h-[48px] border-[#3F3F3F] border-[1px] p-2">
                          Suggested Answer <FaArrowDown className="ml-2" />
                        </button>
                      </a>
                    </div>
                  </div>
                </div>
              )}

            {checkExpiry() && (
              <div className="p-4 bg-red-100 border border-red-300 text-yellow-800 rounded-md shadow-sm my-4">
                <div className="flex items-center mb-2">
                  <svg
                    className="w-6 h-6 mr-2 text-yellow-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 8v4.5m0 0L15 12m-3 0L9 12m0 4.5V20m0 0h6m-6 0a9.969 9.969 0 01-5.682-1.736M12 4.5A9.969 9.969 0 0117.682 4.264M12 4.5v6"
                    />
                  </svg>
                  <h3 className="text-lg font-semibold">Status : Expired</h3>
                </div>
                <p>
                  The paper submission date is passed and validity is expired
                </p>
              </div>
            )}

            {error?.detail === '' && !paper?.check_status && (
              <div className="p-4 bg-yellow-100 border border-yellow-300 text-yellow-800 rounded-md shadow-sm my-4">
                <div className="flex items-center mb-2">
                  <svg
                    className="w-6 h-6 mr-2 text-yellow-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 8v4.5m0 0L15 12m-3 0L9 12m0 4.5V20m0 0h6m-6 0a9.969 9.969 0 01-5.682-1.736M12 4.5A9.969 9.969 0 0117.682 4.264M12 4.5v6"
                    />
                  </svg>
                  <h3 className="text-lg font-semibold">Status : Processing</h3>
                </div>
                <p>
                  Solution is submitted and is currently being processed. You
                  will receive an email once it is checked and will also be able
                  to see the result here.
                </p>
              </div>
            )}

            {error?.detail === '' && paper?.check_status && (
              <div className="p-4 bg-green-100 border border-green-300 text-green-800 rounded-md shadow-sm overflow-y-hidden">
                <div className="flex items-center mb-2">
                  <svg
                    className="w-6 h-6 mr-2 text-green-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  <h3 className="text-lg font-semibold">Status: Checked</h3>
                </div>
                <div className="gap-2">
                  <p className="">Remarks :</p>
                  <p className="text-sm my-2 w-11/12 ">
                    {paper.remarks ? paper.remarks : 'No Remarks'}
                  </p>
                </div>
                {/* <p className="text-sm my-4">
                                Your submission has been checked successfully. You can now view the result. If you have any questions or doubts, feel free to ask!
                            </p> */}
              </div>
            )}

            {/* {((error?.detail === "") && (paper?.check_status)) && <div className="hidden md:block">
                        <p className="text-[#000000] font-primary_medium text-[22px] leading-7">
                            Remarks
                        </p>
                        <div className="mt-3 border-[#000000] border-2 p-3 rounded-lg text-[#1D1D1D] text-base leading-7 font-primary bg-white h-24">
                            {paper.remarks ? paper.remarks : "No Remarks"}
                        </div>
                    </div>} */}
          </div>
        ) : (
          <div className="w-full lg:w-[70%] bg-[#EEEEEE] mt-7 p-6 rounded-lg flex flex-col max-h-[80vh] gap-4 justify-between overflow-hidden">
            <div className="p-4 bg-blue-100 border border-blue-300 text-blue-800 rounded-md shadow-sm my-4">
              <div className="flex items-center mb-2">
                <svg
                  className="w-6 h-6 mr-2 text-blue-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 8v4m0 4h.01M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z"
                  />
                </svg>
                <h3 className="text-lg font-semibold">Status: Coming Soon</h3>
              </div>
              <p>The paper will be available soon. Stay tuned for updates!</p>
            </div>
          </div>
        )}
        {error?.detail === '' && paper?.check_status && (
          <div className="ml-0 lg:ml-auto lg:mt-0 mt-10 w-full lg:w-[25%] flex flex-col justify-around">
            <div className="">
              <p className="text-[#1D1D1D] font-primary_medium text-[22px] leading-7">
                Doubts
              </p>
              <div className="w-full my-2">
                <DoubtModal getDoubts={getDoubts} submissionId={paper.id} />
              </div>
              {doubts?.length === 0 && (
                <div className="my-4 text-[#7D7D7D] font-primary_medium text-base leading-5">
                  <p>No Doubts Asked</p>
                </div>
              )}
              <div className="space-y-3 pt-3 lg:overflow-y-auto lg:max-h-[40vh]">
                {doubts &&
                  doubts.length > 0 &&
                  doubts?.reverse().map((doubt) => (
                    <div className="bg-[#EEEEEE] p-3 rounded-lg">
                      <div className="flex justify-between items-center">
                        {doubt.doubt_status ? (
                          <p className="text-[#03A500] font-primary_medium text-base leading-5">
                            Answered
                          </p>
                        ) : (
                          <p className="text-[#2F50FF] font-primary_medium text-base leading-5">
                            In Process
                          </p>
                        )}
                        <p className="text-[#1D1D1D] font-primary_medium text-sm leading-5">
                          {convertTimestamp(doubt.created_at)}
                        </p>
                      </div>
                      {doubt.attached_object && (
                        <div className="text-green-600 mt-4 text-xs leading-5 font-primary flex gap-2">
                          <File className="w-4 h-4" />
                          <span>File was attached</span>
                        </div>
                      )}
                      <div className="text-[#1D1D1D] mt-4 text-base leading-5 font-primary flex gap-2">
                        <img
                          loading="lazy"
                          src="/icons/question.svg"
                          className="w-4 h-4"
                        />
                        <span>{doubt.question}</span>
                      </div>
                      {doubt.doubt_status && (
                        <div className="text-[#1D1D1D] mt-4 text-base leading-5 font-primary flex gap-2">
                          <img
                            loading="lazy"
                            src="/icons/answer.svg"
                            className="w-4 h-4"
                          />
                          <span>{doubt.answer}</span>
                        </div>
                      )}
                    </div>
                  ))}
              </div>
            </div>
          </div>
        )}
      </div>
      {/* {((error?.detail === "") && (paper?.check_status)) && <div className="md:hidden">
                <p className="my-4 text-[#000000] font-primary_medium text-[22px] leading-7">
                    Remarks
                </p>
                <div className="mt-3 border-[#000000] border-2 p-3 rounded-lg text-[#1D1D1D] text-base leading-7 font-primary bg-white h-24">
                    {paper.remarks}
                </div>
            </div>} */}
    </div>
  );
};

export default QuesPaperSection;
