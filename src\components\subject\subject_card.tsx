import { Subject } from "@/types/Course.ts";
import { useState, useEffect } from "react";

type SubjectCardProps = {
    additionalClassNames?: string;
    subjectTitle: string;
    subjectDesc: string;
    setSelectedSubjects: (subjects: number[]) => void;
    subject: Subject;
    selectedSubjects: number[];
    setTotalPrice: (arg: number) => void;
    totalPrice: number;
};

const SubjectCard = ({
    additionalClassNames,
    subjectTitle,
    setSelectedSubjects,
    selectedSubjects,
    subject,
    setTotalPrice,
    totalPrice
}: SubjectCardProps) => {
    const [isChecked, setIsChecked] = useState(false);

    // Initialize isChecked state based on selectedSubjects
    useEffect(() => {
        setIsChecked(selectedSubjects.includes(subject.id));
    }, [selectedSubjects, subject]);

    const handleChangeSubject = () => {
        const newSelectedSubjects = isChecked
            ? selectedSubjects.filter((subId) => subId !== subject.id)
            : [...selectedSubjects, subject.id];

        if (isChecked) {
            setTotalPrice(totalPrice - 1);
        } else {
            setTotalPrice(totalPrice + 1);
        }


        setSelectedSubjects(newSelectedSubjects);
        setIsChecked(!isChecked); // Toggle isChecked state

    };

    return (
        <div className={`flex pt-3 pb-3 pr-6 pl-4 justify-between items-center bg-[#EEEEEE] w-full 
        rounded-lg min-h-[62px] ${additionalClassNames}`}>
            <div className="items-start lg:items-center flex lg:flex-row flex-col lg:justify-between lg:space-x-2 w-full">
                <div className="flex items-center gap-6">
                    <input
                        id={`checkbox-${subject.id}`}
                        type="checkbox"
                        checked={isChecked}
                        onChange={handleChangeSubject}
                        className="peer cursor-pointer rounded-sm w-5 h-5"
                    />
                    <div>
                        <label
                            htmlFor={`checkbox-${subject.id}`}
                            className="text-base md:text-lg lg:text-xl font-normal text-[#000000] cursor-pointer"
                        >
                            {(subjectTitle).toLowerCase().replace(/\b\w/g, (char) => char.toUpperCase())}
                        </label>
                        {/* <div className="leading-[20px]">
                            <p className="text-sm text-primary_medium font-medium leading-4 text-[#49454F] text-[12px]">
                                {subjectDesc}
                            </p>
                        </div> */}
                        {/* <p className="text-[#49454F] font-primary text-[12px] leading-4">
                            Discount available on coupons
                        </p> */}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SubjectCard;
