'use client';

import { useEffect, useRef, useState, Suspense } from 'react';
import SecondaryButton from '@/components/ui/SecondaryButton';
import { useRouter } from 'next/navigation';
import { auth } from '@/data/firebase.ts';
import { RecaptchaVerifier, signInWithPhoneNumber } from 'firebase/auth';
import { updateUser, selectUser } from '@/redux/features/userSlice.ts';
import { useDispatch, useSelector } from 'react-redux';
import {
  useCreateUserAuthMutation,
  useUpdateUserDetailsMutation,
} from '@/redux/apiSlice';
import { sliceIndianCountryCode } from '@/lib/utils';
import { baseUrl } from '@/config/constants';
import { IoMdArrowBack } from 'react-icons/io';
import { ClipLoader } from 'react-spinners';
import { toast, Toaster } from 'react-hot-toast';
import Image from 'next/image';
import Loading from '@/app/auth/login/loading';

declare const window: any;

type InputProps = {
  length?: number;
  onComplete: (pin: string) => void;
  OTP: string[];
  setOTP: (arg: string[]) => void;
};

const LoginClient = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const user = useSelector(selectUser);
  const [showOTPSection, setShowOTPSection] = useState(false);
  const [phone, setPhone] = useState<string>('');
  const [phoneError, setPhoneError] = useState<string>('');
  const [isPhoneValid, setIsPhoneValid] = useState<boolean>(false);
  const [OTP, setOTP] = useState<string[]>(Array(4).fill(''));
  const [userConfirmation, setUserConfirmation] = useState<any>(null);
  const [createUserAuth] = useCreateUserAuthMutation();
  const [updateUserAPI] = useUpdateUserDetailsMutation();
  const isAuthTokenExpiredStr: string | null =
    localStorage.getItem('isAuthTokenExpired');
  const isAuthTokenExpired =
    isAuthTokenExpiredStr !== null ? JSON.parse(isAuthTokenExpiredStr) : false;
  const [recaptchaLoading, setRecaptchaLoading] = useState(false);
  const [otpLoading, setOTPLoading] = useState(false);
  const [verifyLoading, setVerifyLoading] = useState(false);
  const [isLoginProcess, setIsLoginProcess] = useState(false);
  const [resending, setResending] = useState(false);
  const [showChild, setShowChild] = useState(false);
  const [timer, setTimer] = useState(60);

  useEffect(() => {
    setShowChild(true);
  }, []);

  useEffect(() => {
    if (
      user.firebaseUID != undefined &&
      user.firebaseUID.length != 0 &&
      isAuthTokenExpired == false
    ) {
      localStorage.setItem('isUserLoggedIn', 'true');
      router.push('/dashboard');
    }
  }, [user, isAuthTokenExpiredStr, router]);

  const handleOTPSubmit = (pin: string) => {
    verifyOTP(pin);
  };

  // Function to validate phone number
  const validatePhoneNumber = (phoneNumber: string) => {
    const indianPhoneNumberRegex = /^[6-9][0-9]{9}$/;
    return indianPhoneNumberRegex.test(phoneNumber);
    // return true
  };

  // Handle phone input change
  const handlePhoneChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const input = event.target.value;
    // Remove non-digit characters
    const sanitizedInput = input.replace(/\D/g, '');
    setPhone(sanitizedInput);

    if (sanitizedInput.length > 10) {
      setPhoneError('Phone number cannot exceed 10 digits.');
      setIsPhoneValid(false);
    } else if (sanitizedInput.length < 10) {
      setPhoneError('Phone number must be 10 digits.');
      setIsPhoneValid(false);
    } else if (!validatePhoneNumber(sanitizedInput)) {
      setPhoneError('Please enter a valid Indian phone number.');
      setIsPhoneValid(false);
    } else {
      setPhoneError('');
      setIsPhoneValid(true);
    }
  };

  const handleSendOTP = async () => {
    setRecaptchaLoading(true);
    setPhoneError('');
    // No need to validate here as it's already validated on change
    try {
      // Initialize reCAPTCHA verifier only if it's not initialized already
      // if (!window.recaptchaVerifier) {
      if (!resending && !window.recaptchaVerifier) {
        window.recaptchaVerifier = new RecaptchaVerifier(auth, 'recaptcha', {
          size: 'invisible', // Invisible reCAPTCHA
          callback: () => {
            // console.log('Invisible reCAPTCHA solved:', response);
          },
          'expired-callback': () => {
            console.log('Invisible reCAPTCHA expired, please try again.');
          },
        });
      }
      // }

      const appVerifier = window.recaptchaVerifier;

      setRecaptchaLoading(false);
      setOTPLoading(true);
      setResending(true);
      setTimer(60);

      const phoneNumber = '+91' + phone;
      const result = await signInWithPhoneNumber(
        auth,
        phoneNumber,
        appVerifier
      );
      setUserConfirmation(result);
      setShowOTPSection(true);
      toast.success('OTP sent successfully!');

      const countdown = setInterval(() => {
        setTimer((prev) => {
          if (prev === 1) {
            clearInterval(countdown);
            setResending(false);
            return 30;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(countdown);
    } catch (error) {
      console.error('Error during OTP sending:', error); // Log full error details
      toast.error('Failed to send OTP. Please try again.');
    } finally {
      setOTPLoading(false);
      setRecaptchaLoading(false);
    }
  };

  const handleResendOTP = () => {
    handleSendOTP();
  };

  useEffect(() => {
    return () => {
      if (window.recaptchaVerifier) {
        window.recaptchaVerifier = null;
      }
    };
  }, []);

  const handleReferralProfile = async (accessToken: string) => {
    try {
      const response = await fetch(`${baseUrl}/referral-profile/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: accessToken,
        },
      });

      if (response.ok) {
        const referralData = await response.json();
        return referralData;
      } else {
        const errorData = await response.json();
        console.error('Failed to get or create referral profile:', errorData);
        throw new Error('Failed to get or create referral profile');
      }
    } catch (error) {
      console.error('Error in handling referral profile:', error);
      throw error;
    }
  };

  const verifyOTP = async (otp: string) => {
    setVerifyLoading(true);
    try {
      // Confirm OTP
      const otpData = await userConfirmation.confirm(otp);
      if (otpData) {
        const bodyData = {
          phone: phone,
          firebase_uid: otpData.user.uid,
        };

        // Create user auth
        const { data: res } = await createUserAuth({ body: bodyData });
        const { numberWithoutCountryCode } = sliceIndianCountryCode(phone);

        if (res) {
          try {
            // Update user API
            await updateUserAPI({
              userId: String(res.user_id),
              phone: numberWithoutCountryCode,
              firebase_uid: otpData.user.uid,
              headerToken: res.access,
            }).unwrap();

            // Fetch user details
            const userRes = await fetch(`${baseUrl}/users/${res.user_id}`, {
              headers: {
                Authorization: res.access,
              },
            });
            const userDetails = await userRes.json();
            if (userRes.status === 200 || userRes.status === 201) {
              // Handle referral profile
              try {
                await handleReferralProfile(res.access);
              } catch (referralError) {
                // Just log the error but don't block login process
                console.error('Referral profile setup failed:', referralError);
              }
              setIsLoginProcess(true);
              localStorage.setItem('isUserLoggedIn', 'true');
              router.push('/dashboard');
              dispatch(
                updateUser({
                  userId: res.user_id,
                  phoneNumber: otpData.user.phoneNumber,
                  firebaseUID: otpData.user.uid,
                  emailVerified: otpData.user.emailVerified,
                  providerApiKey: otpData.user.apiKey,
                  tokenResponse: {
                    refreshToken: otpData._tokenResponse.refreshToken,
                    idToken: otpData._tokenResponse.idToken,
                    expiresIn: otpData._tokenResponse.expiresIn,
                    isNewUser: otpData._tokenResponse.isNewUser,
                  },
                  authTokens: {
                    accessToken: res.access,
                    refreshToken: res.refresh,
                  },
                  gender: userDetails?.gender ?? '', // Ensure gender exists
                  email: userDetails?.email ?? '', // Ensure email exists
                  name: userDetails?.name ?? '', // Ensure name exists
                })
              );
            } else {
              throw new Error('Failed to fetch user details');
            }
          } catch (error) {
            toast.error('Internal Server Error, please try again');
            console.error('Update or fetch user error:', error);
          }
        }
      }
    } catch (err) {
      toast.error('OTP Confirmation Failed , Please Try Again');
      console.error('OTP verification error:', err);
    }
    setVerifyLoading(false);
    setOTPLoading(false);
    setRecaptchaLoading(false);
  };

  const handleNavigateBack = () => {
    router.push('/');
  };

  if (!showChild) {
    return null;
  }

  return (
    <Suspense fallback={<Loading />}>
      <div className="min-h-screen w-screen">
        <Toaster />
        <div className="w-full h-[60vh]">
          <button
            onClick={handleNavigateBack}
            className="flex gap-1 items-center justify-center text-neutral-900 font-medium lg:text-xl text-lg absolute left-4 top-4 lg:left-10 lg:top-6"
          >
            <IoMdArrowBack size={20} />
            Back
          </button>
          <Image
            loading="lazy" // Next.js handles lazy loading automatically
            src="/images/gradehunt_auth_image.svg"
            className="object-cover w-full h-full"
            alt="auth_screen_logo"
            width={500} // Set a width based on your design
            height={500} // Set a height based on your design
          />
        </div>

        <div
          className={` ${
            showOTPSection && 'hidden'
          } flex justify-center items-center mt-8 `}
        >
          <div className="flex flex-col">
            <label
              htmlFor="login"
              className="text-[#1D1D1D] mb-1 text-[18px] font-medium font-primary_medium"
            >
              Login
            </label>
            <div className="flex ">
              <span className="px-2 mr-2 py-3 bg-[#F7FAFF] h-[50px] border-[1px] border-[#3F3F3F] rounded-sm">
                +91
              </span>
              <input
                className="w-[250px] sm:w-[320px] h-[50px] mb-6 bg-[#F7FAFF] border-[1px] border-[#3F3F3F] rounded-sm p-4 outline-none"
                placeholder="Enter your mobile number"
                type="text"
                value={phone}
                name="login"
                id="login"
                onChange={handlePhoneChange}
              />
            </div>
            <p
              className={` ${
                phoneError.length === 0 && 'hidden'
              } text-red-500 text-center text-base font-primary_medium `}
            >
              {phoneError.length > 0 && phoneError}
            </p>
            <div
              className="w-1/2 mx-auto mt-4"
              onClick={isPhoneValid ? handleSendOTP : undefined}
            >
              <SecondaryButton
                additionalClassNames="w-full py-4"
                disabled={recaptchaLoading || otpLoading || !isPhoneValid}
              >
                {otpLoading ? (
                  <ClipLoader size={22} color="#ffffff" /> // Spinner inside button
                ) : (
                  'Get OTP'
                )}
              </SecondaryButton>
              {!isLoginProcess && (
                <div className="mt-4" id="recaptcha">
                  {recaptchaLoading && <ClipLoader size={20} color="#000000" />}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* OTP Section */}
        {showOTPSection && (
          <div className="flex flex-col justify-center items-center mt-8 transition-all duration-500 ease-in-out transform">
            <div className="mb-2">
              <p className="text-[#343434] text-left font-medium text-sm">
                Enter OTP sent to {phone}
              </p>
            </div>
            <OTPInput
              length={6}
              onComplete={handleOTPSubmit}
              OTP={OTP}
              setOTP={setOTP}
            />
            <div className="mt-4">
              <span className="text-[#313131] font-normal text-sm mr-2">
                Didn’t receive OTP?
              </span>
              <span
                className={`text-blue-600 font-primary_medium text-sm cursor-pointer ${
                  resending ? 'pointer-events-none opacity-50' : ''
                }`}
                onClick={!resending ? handleResendOTP : undefined}
              >
                {!resending ? 'Resend OTP' : `Resend in ${timer}s`}
              </span>
            </div>
            <div
              className="w-[90%] sm:w-[350px] mx-auto mt-6"
              onClick={() => verifyOTP(OTP.join(''))}
            >
              <SecondaryButton
                additionalClassNames="w-full py-4"
                disabled={verifyLoading}
              >
                {verifyLoading ? (
                  <ClipLoader size={20} color="#ffffff" />
                ) : (
                  'Login'
                )}
              </SecondaryButton>
            </div>
            {!isLoginProcess && (
              <div className="mt-4" id="recaptcha-resend">
                {recaptchaLoading && <ClipLoader size={20} color="#000000" />}
              </div>
            )}
          </div>
        )}
      </div>
    </Suspense>
  );
};

export default LoginClient;

const OTPInput = ({ length = 6, OTP, setOTP }: InputProps) => {
  const inputRef = useRef<HTMLInputElement[]>(Array(length).fill(null));

  const handleTextChange = (input: string, index: number) => {
    // If more than one character is pasted (like 123456)
    if (input.length > 1) {
      const newPin = [...OTP];
      input.split('').forEach((char, idx) => {
        if (index + idx < length) {
          newPin[index + idx] = char;
        }
      });
      setOTP(newPin);

      // Move focus to the last filled input
      const nextIndex = Math.min(index + input.length, length - 1);
      inputRef.current[nextIndex]?.focus();
    } else {
      // Handle single-character input
      if (/^[0-9]$/.test(input) || input === '') {
        const newPin = [...OTP];
        newPin[index] = input;
        setOTP(newPin);

        if (input.length === 1 && index < length - 1) {
          inputRef.current[index + 1]?.focus();
        }

        if (input.length === 0 && index > 0) {
          inputRef.current[index - 1]?.focus();
        }
      }
    }
  };

  return (
    <div
      className={`grid grid-cols-${length} grid-flow-col lg:gap-x-3 gap-x-2`}
    >
      {Array.from({ length }, (_, index) => (
        <input
          key={index}
          inputMode="numeric"
          value={OTP[index]}
          onChange={(e) => handleTextChange(e.target.value, index)}
          onPaste={(e) =>
            handleTextChange(e.clipboardData.getData('Text'), index)
          } // Handle paste
          ref={(ref) => {
            if (ref) {
              inputRef.current[index] = ref as HTMLInputElement;
            }
          }}
          className={`appearance-none border-[#112C79] text-center w-12 h-12 rounded-sm border-[1px] bg-[#F7FAFF] outline-none`}
          maxLength={1}
        />
      ))}
    </div>
  );
};
