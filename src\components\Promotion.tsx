'use client';

import { baseUrl } from '../config/constants';
import PromotionSection from './landing/PromotionSection';
import { useState, useEffect } from 'react';

async function fetchPromotions(course: string) {
  const res = await fetch(
    `${baseUrl}/promotions/?course_group__name=${course}`,
    {
      method: 'GET',
      cache: 'no-store',
    }
  );
  if (!res.ok) {
    throw new Error('Failed to fetch promotions');
  }
  const data = await res.json();
  return data;
}

const HeroImageSection = ({ course = '' }) => {
  const [promotionBanners, setPromotionBanners] = useState<any>({
    count: 0,
    next: null,
    previous: null,
    results: [],
  });

  useEffect(() => {
    let isMounted = true;

    const getPromotions = async () => {
      try {
        const data = await fetchPromotions(course);
        if (isMounted) setPromotionBanners(data);
      } catch (error) {
        console.error('Error fetching promotions:', error);
      }
    };

    getPromotions();

    return () => {
      isMounted = false;
    };
  }, [course]); // Only refetch if course changes

  return (
    <div className="overflow-x-hidden relative">
      <PromotionSection promotionBanners={promotionBanners} />
    </div>
  );
};
export default HeroImageSection;
