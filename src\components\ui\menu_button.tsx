
function MenuButton( {isOpen , setIsOpen} : { isOpen : boolean ,setIsOpen : any  } ) {
  return (
    <button
      onClick={() => setIsOpen(!isOpen)}
      className='flex flex-col gap-1 items-center justify-center'
    >
      <div
        className={`bg-[#FCFCFC] w-6 h-0.5 transition-transform duration-300 ${
          isOpen ? 'transform translate-y-1.5 rotate-45' : ''
        }`}
      ></div>
      <div
        className={`bg-[#FCFCFC] w-6 h-0.5 transition-opacity duration-300 ${
          isOpen ? 'opacity-0' : ''
        }`}
      ></div>
      <div
        className={`bg-[#FCFCFC] w-6 h-0.5 transition-transform duration-300 ${
          isOpen ? 'transform -translate-y-1.5 -rotate-45' : ''
        }`}
      ></div>
    </button>
  );
}

export default MenuButton;
