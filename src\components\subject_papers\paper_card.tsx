import { FaArrowRightLong } from "react-icons/fa6"

const PaperCard = ({ status, paperTitle, marks, duration }: { status: string, paperTitle: string, marks: number, duration: number }) => {
  return (
    <div className="p-4 bg-[#EEEEEE] grid grid-cols-10 md:flex justify-between items-center rounded-lg gap-2">
      <div className="flex flex-col col-span-8">
        <p className="text-[#000000] font-primary_medium leading-7 text-[20px]  ">
          {paperTitle}
        </p>
        {
          status == 'Available' ? (
            <div className="text-[#3F3F3F] flex gap-4 items-start my-4 text-xs md:text-sm lg:text-base">
              <div className="flex items-center space-x-2">
                <img loading='lazy' src="/icons/marks.svg" />
                <span className=" font-semibold">{marks} Marks</span>
              </div>
              <div className="flex items-center space-x-2">
                <img loading='lazy' src="/icons/duration.svg" />
                <span className=" font-semibold">{duration} Minutes</span>
              </div>
            </div>
          ) :
            <span className={` ${status == 'Processing' || status == 'Available' ? 'text-[#FFAE1E]' : 'text-[#03A500]'} `} >{status}</span>
        }
      </div>

      <div className="col-span-2 bg-[#FFFFFF] text-[#FFAE1E] hover:bg-[#2F50FF] hover:text-[white] cursor-pointer flex justify-center items-center rounded-full p-3 lg:ml-0 ml-auto">
        <FaArrowRightLong size={14} className="w-full h-full ease-in-out transition-all duration-200 " />
      </div>
    </div>
  )
}
export default PaperCard


