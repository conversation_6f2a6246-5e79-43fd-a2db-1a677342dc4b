'use client'

import QuesPaperSection from '@/components/ques_papers/ques_paper_section';
import { useParams } from 'next/navigation';

const QuesPaperPage = () => {
  const params = useParams();
  const paperId: string = Array.isArray(params.paperId) ? params.paperId[0] : params.paperId;
  
  return (
    <section className="lg:px-[100px] px-[20px] pt-8 pb-10 lg:pt-16 lg:pb-28">
      <QuesPaperSection paperId={paperId} />
    </section>
  );
};

export default QuesPaperPage;
