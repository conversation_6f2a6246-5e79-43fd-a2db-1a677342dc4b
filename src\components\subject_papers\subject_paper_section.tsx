// import { FaArrowDownLong } from "react-icons/fa6"
import PaperCard from './paper_card';
import { useGetTestPaperSubjectQuery } from '@/redux/apiSlice';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import FilterModal from '../test_series/filter_modal';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { setSelectedTestPaperStatusData } from '@/redux/features/testPaperSlice';
import { ClipLoader } from 'react-spinners';

const SubjectPaperSection: React.FC = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const isOpen = useSelector((state: RootState) => state.filterModal.isOpen);
  const user = useSelector((state: RootState) => state.user);
  const selectedTestPaper = useSelector(
    (state: RootState) => state.testPaper.selectedTestPaper
  );
  const [spinnerColor] = useState('#30b954');

  const {
    data: testStatusData,
    isLoading,
    isFetching,
  } = useGetTestPaperSubjectQuery({
    headerToken: user.tokenResponse.idToken || user.authTokens.accessToken,
  });

  useEffect(() => {
    if (
      (!user.tokenResponse.idToken ||
        user.tokenResponse.idToken.length === 0) &&
      (!user.authTokens.accessToken || user.authTokens.accessToken.length === 0)
    ) {
      router.push('/dashboard');
    }
  }, [
    user.tokenResponse.idToken,
    user.authTokens.accessToken,
    testStatusData,
    router,
  ]);

  const handleSelectPaperPage = (paper: any) => {
    const tempPaperSelected = {
      id: paper.id,
      test_title: paper.test_title,
      test_series: paper.test_series,
      subject: paper.subject?.id,
      valid_from: paper.valid_from,
      valid_to: paper.valid_to,
      time_duration: paper.time_duration,
      total_marks: paper.total_marks,
      question_paper: paper.question_paper ?? null,
      answer_key: paper.answer_key ?? null,
      status: paper.check_status,
    };
    dispatch(setSelectedTestPaperStatusData(tempPaperSelected));
    router.push(`/purchased/subject/paper/${paper.id}`);
  };

  const currentDate = new Date(); // Get today's date

  const filterValidPapers = (papers: any[]) => {
    return papers.filter((paper) => {
      //To check valid from date and display paper after valid date
      const validFromDate = new Date(
        parseInt(`20${paper.valid_from.slice(6, 8)}`),
        parseInt(paper.valid_from.slice(3, 5)) - 1,
        parseInt(paper.valid_from.slice(0, 2))
      );

      //To check valid to date, to hide expired papers
      const validTillDate = new Date(
        parseInt(`20${paper.valid_to.slice(6, 8)}`),
        parseInt(paper.valid_to.slice(3, 5)) - 1,
        parseInt(paper.valid_to.slice(0, 2))
      );

      return validFromDate <= currentDate && validTillDate >= currentDate;
    });
  };

  if (isLoading || isFetching) {
    return (
      <section className="h-[20vh] flex justify-center items-center lg:px-[100px] px-[20px] lg:pt-16 lg:pb-28">
        <ClipLoader
          color={spinnerColor}
          loading={isLoading || isFetching}
          size={150}
          aria-label="Loading Spinner"
          data-testid="loader"
        />
      </section>
    );
  }

  return (
    <div className="w-full">
      <FilterModal isOpen={isOpen} />
      <div className="flex lg:px-6 lg:py-2 rounded-lg lg:border-none border-b-[1px] border-[#D9D9D9] justify-between items-center bg-none lg:bg-[#CFE0FF] min-h-[68px]">
        <div className="flex flex-col items-start">
          <p className="text-[#1D1D1D] font-primary_medium text-sm md:text-base lg:text-[22px] leading-7 ">
            {(selectedTestPaper?.subject.title || '')
              .toLowerCase()
              .replace(/\b\w/g, (char) => char.toUpperCase())}
          </p>
          <p className="text-[#3F3F3F] font-primary text-[12px] text-sm md:text-base leading-5 ">
            {selectedTestPaper?.test_series.test_series_type.title}
          </p>
        </div>
        <div className="mr-2 flex flex-col items-start">
          <p className="text-[#1D1D1D] font-primary text-sm lg:text-base leading-5 ">
            Valid Till
          </p>
          <p className="text-[#3F3F3F] font-primary_medium text-[12px] lg:text-base leading-5 ">
            {selectedTestPaper?.test_series.valid_till}
          </p>
        </div>
      </div>

      <div className="flex flex-col gap-2 mt-4">
        {selectedTestPaper &&
        filterValidPapers(selectedTestPaper.subject.tests).length > 0 ? (
          filterValidPapers(selectedTestPaper.subject.tests).map(
            (paper, index) => (
              <div
                key={index}
                onClick={() => handleSelectPaperPage(paper)}
                className="cursor-pointer"
              >
                <PaperCard
                  paperTitle={paper.test_title}
                  status={paper.check_status}
                  marks={paper.total_marks}
                  duration={paper.time_duration}
                />
              </div>
            )
          )
        ) : (
          <p className="text-yellow-600 mt-10 text-center text-lg font-primary_medium">
            No Test Paper Found!
          </p>
        )}
      </div>
    </div>
  );
};

export default SubjectPaperSection;
