import { toast, Toaster } from 'react-hot-toast';
import { useEffect, useState } from 'react';
import SubjectCard from './subject_card';
import SecondaryButton from '../ui/SecondaryButton';
import {
  PurchasedSubject,
  PurchasedTestItem,
  PurchasedTests,
  TestSeriesType,
} from '@/types/Test.ts';
import { baseUrl } from '@/config/constants';
import { ClipLoader } from 'react-spinners';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import axios from 'axios';
import {
  useCreateUserAuthMutation,
  useGetReferalsQuery,
} from '@/redux/apiSlice';
import { updateUser } from '@/redux/features/userSlice';

type PerCostSubjectProps = {
  onOpen: any;
  selectedTestSeries: TestSeriesType | null;
  totalPrice: number;
  setTotalPrice: (arg: number) => void;
  selectedSubjects: number[];
  setSelectedSubjects: (arg: number[]) => void;
  setDiscountedFinalPrice: any;
  isLoading: boolean;
  onCouponSelect: (code: number) => void;
  phoneNum: string;
};

type Discount = {
  id: number;
  title: string;
  discount_percent: string;
  code: string;
};

const PerCostSubject = ({
  onOpen,
  selectedTestSeries,
  totalPrice,
  setTotalPrice,
  selectedSubjects,
  setSelectedSubjects,
  setDiscountedFinalPrice,
  isLoading,
  onCouponSelect,
  phoneNum,
}: PerCostSubjectProps) => {
  const [coupons, setCoupons] = useState<Discount[]>([]);
  const [filteredCoupons, setFilteredCoupons] = useState<Discount[]>([]);
  const [couponInput, setCouponInput] = useState<string>('');
  const [discountValue, setDiscountValue] = useState(0);
  const [inputFocused, setInputFocused] = useState(false);
  const currentUser = useSelector((state: RootState) => state.user);
  const purchasedCourses: PurchasedTests = useSelector(
    (state: RootState) => state.purchasedCourses.purchasedCourses
  );
  const [boughtSubjects, setBoughtSubjects] = useState<PurchasedSubject[]>([]);
  const [blurTimeout, setBlurTimeout] = useState<NodeJS.Timeout | null>(null);
  const [createUserAuth] = useCreateUserAuthMutation();
  const dispatch = useDispatch();
  const [access, setAccess] = useState(currentUser.authTokens.accessToken);

  const handleFocus = () => {
    if (blurTimeout) {
      clearTimeout(blurTimeout);
      setBlurTimeout(null);
    }
    setInputFocused(true);
  };

  const handleBlur = () => {
    const timeout = setTimeout(() => {
      setInputFocused(false);
    }, 200); // Adjust delay as needed
    setBlurTimeout(timeout);
  };

  const { data: userReferals } = useGetReferalsQuery(
    {
      headerToken: currentUser.tokenResponse?.idToken,
      userId: currentUser.userId?.toString() || '',
    },
    {
      skip: !currentUser?.tokenResponse?.idToken,
      refetchOnMountOrArgChange: true,
    }
  );

  const handleGetAccessToken = async () => {
    const bodyData = {
      phone: phoneNum,
    };
    const { data: accessRes } = await createUserAuth({ body: bodyData });

    if (accessRes) {
      dispatch(
        updateUser({
          userId: accessRes.user_id,
          authTokens: {
            accessToken: accessRes.access,
            refreshToken: accessRes.refresh,
          },
          phoneNumber: phoneNum,
        })
      );
      setAccess(accessRes.access);
    }
    return [accessRes.access, accessRes.user_id];
  };

  useEffect(() => {
    const fetchData = async () => {
      if (purchasedCourses.length > 0) {
        const tests = purchasedCourses.filter(
          (course) => course.test_series.id === selectedTestSeries?.id
        );
        const subjects = tests.map((test) => test.subjects[0]);
        if (subjects.length > 0) {
          setBoughtSubjects(subjects);
        }
      } else if (currentUser.authTokens.accessToken) {
        axios
          .get(
            `${baseUrl}/get-bought-subjects/?test_series_id=${selectedTestSeries?.id}`,
            {
              headers: {
                Authorization: access || currentUser?.authTokens?.accessToken,
              },
            }
          )
          .then((res) => {
            const tests: PurchasedTestItem[] = res.data;
            const subjects = tests.map((test) => test.subjects[0]);
            setBoughtSubjects(subjects);
            console.log(res.data);
          })
          .catch((err) => {
            if (err.status == 403) {
              handleGetAccessToken();
            }
          });
      }
    };

    fetchData();
  }, [purchasedCourses, currentUser]);

  useEffect(() => {
    const getCoupons = async () => {
      const couponRes = await fetch(`${baseUrl}/coupon/`);
      const couponData = await couponRes.json();
      if (couponRes.status === 200 || couponRes.status === 201) {
        setCoupons(couponData);
      }
    };
    getCoupons();
  }, []);

  useEffect(() => {
    const filtered =
      inputFocused && couponInput === ''
        ? coupons
        : coupons.filter((coupon) =>
            coupon.code.toUpperCase().includes(couponInput.toUpperCase())
          );
    setFilteredCoupons(filtered);
  }, [couponInput, coupons, inputFocused]);

  const verifyCoupon = async (code: string) => {
    try {
      if (
        userReferals?.referral_code?.code &&
        code.toUpperCase() === userReferals.referral_code.code.toUpperCase()
      ) {
        toast.error('You cannot use your own referral code as a coupon');
        setDiscountValue(0);
        onCouponSelect(-1);
        return;
      }

      const res = await axios.post(
        `${baseUrl}/coupon/validate_code/`,
        { code },
        {
          headers: {
            Authorization: `${currentUser.tokenResponse.idToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (res.status === 200) {
        const coupon: Discount = res.data;
        setCouponInput(coupon.code);
        setDiscountValue(parseFloat(coupon.discount_percent));
        toast.success(`Coupon applied: ${coupon.discount_percent}% off!`);
        onCouponSelect(coupon.id);
      } else {
        toast.error('No such coupon available');
        setDiscountValue(0);
        onCouponSelect(-1);
      }
    } catch (error) {
      toast.error('Error verifying coupon. Try Valid Coupon');
      onCouponSelect(-1);
    }
  };

  const handleCouponSelection = (code: string) => {
    setCouponInput(code);
    verifyCoupon(code);
  };

  const discountedTotal =
    ((selectedTestSeries?.discounted_price
      ? selectedTestSeries?.discounted_price
      : selectedTestSeries?.mrp_price) ?? 0) *
    totalPrice *
    (1 - discountValue / 100);

  useEffect(() => {
    setDiscountedFinalPrice(discountedTotal);
  }, [discountedTotal]);

  if (selectedTestSeries == null) {
    return null;
  }

  return (
    <div className="flex flex-col lg:flex-row max-w-7xl mx-auto lg:gap-0 gap-12 mt-6">
      <Toaster />
      <div className="w-full lg:w-[60%] space-y-4">
        <div className="flex pt-3 pb-3 pr-6 pl-4 rounded-lg justify-between items-center bg-[#CFE0FF] min-h-[74px]">
          <div className="flex flex-col items-start">
            <p className="text-[#1D1D1D] font-primary_medium text-base lg:text-[22px] leading-7">
              Price per subject
            </p>
            <p className="text-[#49454F] font-primary text-[12px] lg:text-base leading-5 ">
              {selectedTestSeries.title}
            </p>
          </div>
          {selectedTestSeries.discounted_price ? (
            <div className="text-[#000000] font-primary_medium text-xl lg:text-[22px] leading-7">
              ₹ {selectedTestSeries.discounted_price}{' '}
              <s>₹ {selectedTestSeries.mrp_price}</s>
            </div>
          ) : (
            <div className="text-[#000000] font-primary_medium text-xl lg:text-[22px] leading-7">
              ₹ {selectedTestSeries.mrp_price}
            </div>
          )}
        </div>

        {selectedTestSeries.course_type.subjects.map((subject, index) => {
          // Check if the subject ID is in the boughtSubjects
          const isBought = boughtSubjects?.filter(
            (bought) => bought?.id == subject.id
          );
          // Render SubjectCard only if the subject is not in boughtSubjects
          if (isBought.length == 0) {
            return (
              <SubjectCard
                totalPrice={totalPrice}
                setTotalPrice={setTotalPrice}
                subject={subject}
                selectedSubjects={selectedSubjects}
                setSelectedSubjects={setSelectedSubjects}
                key={index}
                subjectTitle={subject.title}
                subjectDesc={subject.description}
              />
            );
          }
          return null; // Return null if the subject is already bought
        })}
      </div>

      <div className="w-full mx-auto flex flex-col gap-4 lg:w-[30%]">
        {/* Input for Coupon Code */}
        <div className="relative flex items-center space-x-2 h-[6vh]">
          <input
            type="text"
            value={couponInput}
            onChange={(e) => {
              setDiscountValue(0);
              setCouponInput(e.target.value.toUpperCase());
            }}
            onFocus={handleFocus}
            onBlur={handleBlur}
            placeholder="Enter coupon code"
            className="bg-[#FFF3DF] rounded-lg p-6 py-4 flex-1 outline-none h-[6vh]"
          />
          <button
            onClick={() => verifyCoupon(couponInput)}
            className="bg-[#FFAE1E] text-black rounded-lg p-3 h-full"
          >
            Apply
          </button>
        </div>
        <div className="relative">
          {inputFocused &&
            (couponInput === ''
              ? coupons.length > 0
              : filteredCoupons.length > 0) && (
              <div className="absolute z-10 rounded-lg h-72 w-full bg-white bg-opacity-50"></div>
            )}
          {/* Filtered Coupons Dropdown */}
          {inputFocused &&
            (couponInput === ''
              ? coupons.length > 0
              : filteredCoupons.length > 0) && (
              <ul className="absolute z-20 bg-white border rounded-lg max-h-72 overflow-y-auto w-full">
                {(couponInput === '' ? coupons : filteredCoupons).map(
                  (coupon) => (
                    <li
                      key={coupon.id}
                      onClick={() =>
                        handleCouponSelection(coupon.code.toUpperCase())
                      }
                      className="p-3 hover:bg-yellow-50 cursor-pointer rounded-lg"
                    >
                      <p className="text-sm">
                        {coupon.discount_percent}% Discount
                      </p>
                      <strong className="text-[#2F50FF]">
                        {coupon.code.toUpperCase()}
                      </strong>
                    </li>
                  )
                )}
              </ul>
            )}
        </div>

        <div className="bg-[#EEEEEE] p-3 w-full rounded-lg flex justify-start flex-col gap-6">
          <p className="text-[#2F50FF] text-[12px] font-primary_medium leading-4">
            {totalPrice} Subjects selected
          </p>

          <div className="flex">
            <p className="font-primary text-[#1C1B1F] leading-5 text-base">
              Total
            </p>
            <p className="ml-auto font-primary text-[#000000] leading-5 text-base">
              ₹
              {(selectedTestSeries.discounted_price ||
                selectedTestSeries.mrp_price) * totalPrice}
            </p>
          </div>
          <div className="flex">
            <p className="font-primary text-[#1C1B1F] leading-5 text-base">
              Discount
            </p>
            <p className="ml-auto font-primary text-[#000000] leading-5 text-base">
              {discountValue}%
              <span className="text-green-600 ml-2">
                [-₹
                {(
                  (selectedTestSeries.discounted_price ||
                    selectedTestSeries.mrp_price) *
                    totalPrice -
                  parseFloat(discountedTotal.toFixed(2))
                ).toLocaleString()}
                ]
              </span>
            </p>
          </div>
          <div className="flex">
            <p className="font-primary text-[#1C1B1F] leading-5 text-base">
              Final Total
            </p>
            <p className="ml-auto font-primary text-[#000000] leading-5 text-[22px]">
              ₹{discountedTotal.toFixed(2)}
            </p>
          </div>
        </div>

        <div onClick={onOpen}>
          <SecondaryButton additionalClassNames="my-4 w-full">
            {isLoading ? <ClipLoader size={20} color="#ffffff" /> : 'Checkout'}
          </SecondaryButton>
        </div>
      </div>
    </div>
  );
};

export default PerCostSubject;
