
type Props = {
    currentTab : string
    setCurrentTab : any
}

const AboutUsTabs = ({currentTab,setCurrentTab}: Props) => {
  return (

        <div className=''>
            <div className="lg:flex flex-row lg:gap-4 grid lg:grid-cols-3 grid-cols-2 gap-2 lg:justify-start items-center justify-center">
                <div className={` cursor-pointer ${currentTab == 'how_it_works' ? "bg-[#2F50FF] text-[#FCFCFC]" : "bg-[#B7C2FF] text-[#2F50FF]"} lg:text-base text-xs border-[1px] lg:h-auto border-[#2F50FF]  py-2 px-3 rounded-[40px]`} onClick={()=>setCurrentTab('how_it_works')} >
                    How it works
                </div>
                <div className={` cursor-pointer ${currentTab == 'our_unique_features' ? "bg-[#2F50FF] text-[#FCFCFC]" : "bg-[#B7C2FF] text-[#2F50FF]"} lg:text-base text-xs border-[1px] border-[#2F50FF] py-2 px-3 rounded-[40px]`} onClick={()=>setCurrentTab('our_unique_features')}>
                    Our Unique Features
                </div>
                <div className={` cursor-pointer ${currentTab == 'sample_papers' ? "bg-[#2F50FF] text-[#FCFCFC]" : "bg-[#B7C2FF] text-[#2F50FF]"} lg:text-base text-xs border-[1px] border-[#2F50FF] py-2 px-3 rounded-[40px]`} onClick={()=>setCurrentTab('sample_papers')}>
                    Sample Papers
                </div>
            </div>
        </div>
  )
}

export default AboutUsTabs