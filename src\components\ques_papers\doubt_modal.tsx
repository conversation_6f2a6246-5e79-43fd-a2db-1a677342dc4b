import SecondaryButton from "../ui/SecondaryButton";
import { useState, useRef, useEffect } from "react";
import { useCreateDoubtMutation } from "@/redux/apiSlice";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { baseUrl, digital_ocean_base_url } from "@/config/constants";
import toast from "react-hot-toast";
import { Loader } from "lucide-react";
import { auth } from "@/data/firebase";

const MAX_CHAR_COUNT = 2000;

const DoubtModal = ({ submissionId, getDoubts }: { submissionId: number | undefined, getDoubts:() => Promise<void> }) => {
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [doubt, setDoubt] = useState<string>('');
    const currentUser = useSelector((state: RootState) => state.user);
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false); // Track submission state
    const [createDoubt] = useCreateDoubtMutation();
    const [headerToken, setHeaderToken] = useState("");

    useEffect(() => {
        const fetchToken = async () => {
            const currentUser = auth.currentUser;
            if (currentUser) {
                try {
                    const token = await currentUser.getIdToken(true);
                    setHeaderToken(token);
                } catch (error) {
                    console.error('Error fetching ID token:', error);
                }
            }
        };

        fetchToken();
    }, []);
    const fileInputRef = useRef<HTMLInputElement>(null); // Reference for the file input

    const handleCreateDoubt = async () => {
        if (doubt.length < 10) {
            toast('Doubt should be minimum 10 characters');
            return;
        }
        
        setIsSubmitting(true); // Start loader

        try {
            let attachedObject = '';

            if (selectedFile) {
                const fileObjectName = selectedFile.name.replace(/\s+/g, '');

                const urlResponse = await fetch(`${baseUrl}/doubt-presigned-url/?object_key=${fileObjectName}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `${headerToken || currentUser.tokenResponse.idToken}`
                    }
                });

                const urlData = await urlResponse.json();

                if (urlData) {
                    const preSignedUrl = urlData.url;

                    const uploadResponse = await fetch(preSignedUrl, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': selectedFile.type ?? '',
                            'x-amz-acl': 'public-read',
                        },
                        body: selectedFile
                    });

                    if (uploadResponse.status === 200 || uploadResponse.status === 201) {
                        attachedObject = `${digital_ocean_base_url}/doubts/${fileObjectName}`;
                    } else {
                        console.error('Failed to upload attachment', uploadResponse.statusText);
                    }
                }
            }

            const bodyData = {
                submission: submissionId,
                user: currentUser.userId,
                question: doubt,
                attached_object: attachedObject
            };

            const { data: res } = await createDoubt({
                headerToken: headerToken || currentUser.tokenResponse.idToken,
                body: bodyData,
            });
            if (res) {
                toast.success('Doubt Submitted Successfully');
                getDoubts();
            }
        } catch (error) {
            console.error('An error occurred', error);
        } finally {
            setDoubt('');
            setSelectedFile(null);
            setIsSubmitting(false); // Stop loader
        }
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files && event.target.files.length > 0) {
            setSelectedFile(event.target.files[0]);
        }
    };

    const resetFileInput = () => {
        setSelectedFile(null);
        if (fileInputRef.current) {
            fileInputRef.current.value = ''; // Reset the input field
        }
    };

    return (
        <div className="mx-auto w-full">
            <div className="space-y-2">
                <label htmlFor="textarea" className="text-[#3F3F3F] font-primary_medium">Enter Your Doubt</label>
                <div className="border-[1px] border-[#112C79] bg-[#F7FAFF] p-2 rounded-lg">
                    <textarea 
                        value={doubt} 
                        onChange={(e) => setDoubt(e.target.value)} 
                        placeholder="Ask Your doubt ..." 
                        id="textarea" 
                        name="textarea" 
                        className="rounded-sm p-2 w-full outline-none rounded-lg h-[15vh] resize-none border-none" 
                    />
                    {doubt.length <= 2000 ? <div className="text-right text-xs text-gray-500">
                        {doubt.length}/{MAX_CHAR_COUNT} characters
                    </div> :
                    <div className="text-right text-xs text-red-500">
                        {doubt.length}/{MAX_CHAR_COUNT} characters
                    </div>}
                    <div className="grid grid-cols-10 justify-center items-center space-x-1 mt-2">
                        {selectedFile ? (
                            <div className="col-span-6 grid grid-cols-4 flex items-center border border-1 rounded-lg p-1">
                                <p className="col-span-3 overflow-x-auto whitespace-nowrap text-sm p-2">
                                    {selectedFile?.name}
                                </p>
                                <img loading='lazy' onClick={resetFileInput} className="mx-auto cursor-pointer" src={"/icons/cross.svg"} />
                            </div>
                        ) : (
                            <div className="col-span-6" />
                        )}
                        <input
                            type='file'
                            className="hidden"
                            id="doubt-file-upload"
                            ref={fileInputRef} // Attach the ref
                            accept=".pdf, image/*"
                            onChange={handleFileChange}
                        />
                        <label
                            htmlFor="doubt-file-upload"
                            className={`cursor-pointer`}
                        >
                            <div className="cursor-pointer">
                                <img loading='lazy' width={20} src={"/icons/attach.svg"} />
                            </div>
                        </label>
                        {isSubmitting ?
                            <div className="col-span-3 text-xs">
                                <SecondaryButton
                                    additionalClassNames="w-full text-xs"
                                    disabled={doubt.length > MAX_CHAR_COUNT || isSubmitting} // Disable button if character limit exceeded or submitting
                                >
                                    <Loader />
                                </SecondaryButton>
                            </div>
                            : <div className="col-span-3 text-xs" onClick={handleCreateDoubt}>
                                <SecondaryButton 
                                additionalClassNames="w-full text-xs" 
                                disabled={doubt.length > MAX_CHAR_COUNT || isSubmitting} // Disable button if character limit exceeded or submitting
                            >
                                 Ask
                            </SecondaryButton>
                        </div>}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DoubtModal;
