'use client';

import { usePathname } from 'next/navigation';
import { useState } from 'react';
import { FaChevronLeft, FaChevronRight, FaPlay } from 'react-icons/fa';
import Modal from 'react-modal'; // Import the modal library

type Topper = {
  video_thumbnail_url: string | undefined;
  video_url: any;
  banner_url: string | undefined;
  testimonial_type: string;
  name: string;
  description: string;
  // topper_pic_url: string;
  // topper_video_url: string; // Add video URL
  course_type: {
    id: number;
    title: string;
    description: string;
    subjects: {
      id: number;
      title: string;
      description: string;
      course_type: number;
    }[];
    attempts: {
      id: number;
      month: string;
      year: number;
    }[];
  };
  // topper_rank: number;
  // topper_year: number;
};

interface ToppersSectionProps {
  allToppers: Topper[];
}

const MainSwiper: React.FC<ToppersSectionProps> = ({ allToppers }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [modalIsOpen, setModalIsOpen] = useState(false);
  const [currentVideoUrl, setCurrentVideoUrl] = useState<string | null>(null);
  // console.log(allToppers);
  const handlePrevious = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex > 0 ? prevIndex - 1 : allToppers.length - 1
    );
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex < allToppers.length - 1 ? prevIndex + 1 : 0
    );
  };

  const openModalWithVideo = (videoUrl: string) => {
    setCurrentVideoUrl(videoUrl);
    setModalIsOpen(true);
  };

  const closeModal = () => {
    setModalIsOpen(false);
    setCurrentVideoUrl(null);
  };

  const pathname = usePathname();
  return (
    <div className="relative">
      {/* Left navigation button */}
      {currentIndex > 0 ? (
        <div
          className={`absolute top-[50%] -left-5 sm:-left-10 ${
            !modalIsOpen && 'z-20'
          }`}
        >
          <button
            onClick={handlePrevious}
            className="bg-[#FFF] shadow-lg flex justify-center items-center w-[40px] h-[40px] sm:w-[60px] sm:h-[60px] lg:w-[80px] lg:h-[80px] border border-gray-300 rounded-full transition-opacity duration-300 ease-in-out hover:shadow-xl"
          >
            <FaChevronLeft size={32} className="text-[#FFAE1E]" />
          </button>
        </div>
      ) : (
        <div
          className={`absolute top-[50%] -left-5 sm:-left-10 ${
            !modalIsOpen && 'z-20'
          }`}
        >
          <button className="bg-[#FFF] shadow-lg flex justify-center items-center w-[40px] h-[40px] sm:w-[60px] sm:h-[60px] lg:w-[80px] lg:h-[80px] border border-gray-300 rounded-full transition-opacity duration-300 ease-in-out hover:shadow-xl opacity-50">
            <FaChevronLeft size={32} className="text-[#FFAE1E]" />
          </button>
        </div>
      )}

      {/* Right navigation button */}
      {currentIndex < allToppers.length - 1 ? (
        <div
          className={`absolute top-[50%] -right-5 sm:-right-10 ${
            !modalIsOpen && 'z-20'
          }`}
        >
          <button
            onClick={handleNext}
            className="bg-[#FFF] shadow-lg flex justify-center items-center w-[40px] h-[40px] sm:w-[60px] sm:h-[60px] lg:w-[80px] lg:h-[80px] border border-gray-300 rounded-full transition-opacity duration-300 ease-in-out hover:shadow-xl"
          >
            <FaChevronRight size={32} className="text-[#FFAE1E]" />
          </button>
        </div>
      ) : (
        <div
          className={`absolute top-[50%] -right-5 sm:-right-10 ${
            !modalIsOpen && 'z-20'
          }`}
        >
          <button className="bg-[#FFF] shadow-lg flex justify-center items-center w-[40px] h-[40px] sm:w-[60px] sm:h-[60px] lg:w-[80px] lg:h-[80px] border border-gray-300 rounded-full transition-opacity duration-300 ease-in-out hover:shadow-xl opacity-50">
            <FaChevronRight size={32} className="text-[#FFAE1E]" />
          </button>
        </div>
      )}

      {allToppers.length > 0 && (
        <div
          className={`${
            [
              '/course/icse-test-series',
              '/course/cbse-test-series',
              '/course/maharashtra-board-test-series',
              '/boards',
            ].includes(pathname) ||
            allToppers[currentIndex].testimonial_type === 'banner'
              ? 'px-8 bg-white transition-shadow duration-300 ease-in-out'
              : 'flex flex-col lg:flex-row items-center lg:items-start lg:gap-10 p-6 px-8 bg-white transition-shadow duration-300 ease-in-out'
          }`}
        >
          {[
            '/course/icse-test-series',
            '/course/cbse-test-series',
            '/course/maharashtra-board-test-series',
            '/boards',
          ].includes(pathname) ||
          allToppers[currentIndex].testimonial_type === 'banner' ? (
            // New format for specific routes
            <div className="w-full aspect-video">
              <img
                loading="lazy"
                src={allToppers[currentIndex].banner_url}
                alt={`${allToppers[currentIndex].name} Banner`}
                className="absolute inset-0 w-full h-full object-cover rounded-xl sm:object-contain"
              />
            </div>
          ) : (
            // Old format for other routes
            <>
              {/* Left half: Banner with Play Button */}
              <div className="w-full relative aspect-video">
                <img
                  loading="lazy"
                  src={allToppers[currentIndex].video_thumbnail_url}
                  alt={`${allToppers[currentIndex].name} thumbnail`}
                  className="rounded-xl w-full h-full object-cover sm:object-contain"
                />
                {allToppers[currentIndex].video_url && (
                  <button
                    className="absolute inset-0 flex items-center justify-center rounded-xl"
                    onClick={() =>
                      openModalWithVideo(allToppers[currentIndex].video_url)
                    }
                  >
                    <FaPlay
                      size={50}
                      className="text-white p-2 bg-black bg-opacity-50"
                    />
                  </button>
                )}
              </div>

              {/* Right half: Topper's info */}
              {/* <div className="lg:w-1/2 w-full flex flex-col justify-center gap-4 mt-6 lg:mt-0">
                <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-gray-800 hover:text-[#FFAE1E] transition-colors duration-200">
                  {allToppers[currentIndex].name}
                </h2>
                <p className="md:text-lg lg:text-xl text-gray-500 h-[20vh] lg:h-[20vh] overflow-y-hidden">
                  "{allToppers[currentIndex].description}"
                </p>
                <div className="flex gap-2 mt-2 lg:max-h-[8vh] lg:flex-wrap overflow-x-auto lg:overflow-y-auto">
                  <p
                    key={allToppers[currentIndex]?.course_type?.title}
                    className="bg-gray-200 text-gray-700 px-3 py-1 rounded-xl text-base lg:text-lg transition-colors duration-200 hover:bg-[#FFAE1E] hover:text-white whitespace-nowrap"
                  >
                    {allToppers[currentIndex]?.course_type?.title}
                  </p>
                </div>
              </div> */}
            </>
          )}
        </div>
      )}
      {/* Modal for video */}
      <Modal
        isOpen={modalIsOpen}
        onRequestClose={closeModal}
        contentLabel="Topper Video Modal"
        className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-75"
        ariaHideApp={false}
      >
        <div className="relative w-[90%] lg:w-[70%] h-[60vh] lg:h-[70vh] bg-white rounded-lg">
          <button
            className="absolute top-2 right-2 w-8 h-8 text-center text-black bg-white rounded-xl p-1"
            onClick={closeModal}
          >
            X
          </button>
          <iframe
            className="w-full h-full rounded-lg"
            src={currentVideoUrl || ''}
            title="Topper Video"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          />
        </div>
      </Modal>
    </div>
  );
};

export default MainSwiper;
