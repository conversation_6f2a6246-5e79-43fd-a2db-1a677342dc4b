import { baseUrl } from '@/config/constants';
import FacultiesSection from './FacultiesSection';

async function fetchFaculties(course: string) {
  const res = await fetch(`${baseUrl}/faculty/?course_group__name=${course}`, {
    method: 'GET',
    cache: 'no-store',
  });
  if (!res.ok) {
    throw new Error('Failed to fetch faculties');
  }
  const data = await res.json();
  return data.results;
}

const Faculties = async ({ course = '' }: { course?: string }) => {
  let faculties = [];

  try {
    const data = await fetchFaculties(course);
    faculties = data;
  } catch (error) {
    console.error('Error fetching faculties:', error);
  }

  return (
    <div>
      <FacultiesSection allFaculties={faculties} />
    </div>
  );
};

export default Faculties;
