type Props = {
    title : string;
    additionalClassNames?: string;
}

const PrimaryButton = ({ title,additionalClassNames }: Props) => {
  return (
    <button className={`rounded-[33px] text-[20px] text-[#2F50FF] font-medium  cursor-pointer tracking-[0.5%] bg-[#FFFFFF] py-1.5 px-[24px]
    hover:border-[#2F50FF] hover:bg-[#2F50FF] hover:text-[#FFFFFF] transition-all duration-150 ${additionalClassNames} `}>
        {title}
    </button>
  )
}

export default PrimaryButton