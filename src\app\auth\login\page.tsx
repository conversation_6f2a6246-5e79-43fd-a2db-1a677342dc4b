import React from 'react';
import dynamic from 'next/dynamic';
import { Metadata } from 'next';

const NoSSRLoginClient = dynamic(
  () => import('@/components/auth/LoginClient'),
  {
    ssr: false,
  }
);

export const metadata: Metadata =
  process.env.NEXT_PUBLIC_IS_PROD == 'true'
    ? {
        title: 'Student Login | Gradehunt',
        description:
          'Login to the student portal by entering your mobile number. Get an OTP and sign up for Gradehunt to access your account.',
        openGraph: {
          type: 'website',
          url: 'https://www.gradehunt.com/auth/login',
          title: 'Student Login | Gradehunt',
          description:
            'Login to the student portal by entering your mobile number. Get an OTP and sign up for Gradehunt to access your account.',
          images: [
            {
              url: 'https://gradehunt.com/icons/gradehunt_logo.svg',
              alt: 'Gradehunt Logo',
            },
          ],
        },
        twitter: {
          card: 'summary_large_image',
          title: 'Student Login | Gradehunt',
          description:
            'Login to the student portal by entering your mobile number. Get an OTP and sign up for Gradehunt to access your account.',
          images: ['https://gradehunt.com/icons/gradehunt_logo.svg'],
        },
      }
    : {
        title: 'Student Login | Gradehunt',
        description:
          'Login to the student portal by entering your mobile number. Get an OTP and sign up for Gradehunt to access your account.',
      };

const Login = () => {
  return (
    <>
      <NoSSRLoginClient />;
    </>
  );
};

export default Login;
