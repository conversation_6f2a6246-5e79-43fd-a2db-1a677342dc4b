import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import faqsData from '../faqs.json';

interface Faq {
  id: number;
  question: string;
  answer: string;
}

interface FAQSectionProps {
  ids: number[];
}

const FAQSection = ({ ids }: FAQSectionProps) => {
  // Filter the FAQs based on the passed ids
  const filteredFaqs: Faq[] = faqsData.filter((faq: Faq) => ids?.includes(faq.id));

  return (
    <section className=" px-[20px] py-8 lg:py-16 lg:pb-8 lg:px-[100px] lg:pt-20">
      <h2 className="block font-primary_medium text-2xl md:text-3xl lg:text-4xl my-4">Frequently Asked Questions</h2>
      <div className="w-full" >
        <Accordion type="single" collapsible className="mx-auto max-w-sm lg:max-w-4xl">
          {filteredFaqs.map((faq) => (
            <AccordionItem
              key={faq.id} // Use faq.id as key
              value={`item-${faq.id}`} // Assign a unique value for each item
              className="font-medium text-[16px] text-[#3F3F3F] px-4 rounded-[8px] mb-4 bg-[#EEEEEE]"
            >
              <AccordionTrigger>{faq.question}</AccordionTrigger>
              <AccordionContent className="text-[#808080] font-[16px] leading-8 whitespace-pre-line">
                {faq.answer}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </section>
  );
};

export default FAQSection;