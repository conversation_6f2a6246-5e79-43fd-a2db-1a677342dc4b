import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import parse from "html-react-parser";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(dateStr: string): string {
  const date = new Date(dateStr);
  const options: Intl.DateTimeFormatOptions = { day: '2-digit', month: 'short', year: 'numeric' };
  return date.toLocaleDateString('en-GB', options);
}

export function sliceIndianCountryCode(phoneNumber : string) {
  const indianCountryCode = '+91';
  
  if (phoneNumber.startsWith(indianCountryCode)) {
    const numberWithoutCountryCode = phoneNumber.slice(indianCountryCode.length);
    return { countryCode: indianCountryCode, numberWithoutCountryCode };
  } else {
    // If the phone number does not start with the Indian country code, return the original number without a country code
    return { countryCode: '', numberWithoutCountryCode: phoneNumber };
  }
}

export function parseWithBR (htmlString : string) {
  return parse(htmlString?.replace(/<p[^>]*><\/p>/g, "<br />"))
}