'use client';

import { useState, useRef, useEffect } from 'react';

type Banner = {
  id: number;
  image_id: number;
  title: string;
  url: string;
};

interface FeaturesPromotionSectionProps {
  promotionBanners: Banner[];
}

const FeaturesPromotionSection: React.FC<FeaturesPromotionSectionProps> = ({ promotionBanners }) => {
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const startAutoSlide = () => {
    intervalRef.current = setInterval(() => {
      setCurrentBannerIndex((prevIndex) =>
        prevIndex === promotionBanners?.length - 1 ? 0 : prevIndex + 1
      );
    }, 2500); // 2.5 seconds per slide
  };

  const handleDotClick = (index: number) => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    setCurrentBannerIndex(index);
    startAutoSlide(); // Reinitialize the interval after a dot is clicked
  };

  useEffect(() => {
    startAutoSlide();
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [promotionBanners]);

  if (promotionBanners?.length === 0) {
    return <div>Loading banners...</div>;
  }

  return (
    <div className="relative w-full h-full overflow-x-hidden">
      <div className="w-full h-full">
        <div
          className="hidden md:flex w-full h-full transition-transform duration-1000 ease-in-out"
          style={{ transform: `translateX(-${currentBannerIndex * 100}%)` }}
        >
          {promotionBanners?.map((banner, index) => (
            <div key={index} className="w-full h-full flex-shrink-0">
              <img
                width={2560}
                height={860}
                loading={currentBannerIndex === index ? 'eager' : 'lazy'}
                src={banner.url}
                alt={banner.title}
                className="w-full h-full object-cover"
              />
            </div>
          ))}
        </div>
        <div
          className="w-full h-full flex md:hidden transition-transform duration-1000 ease-in-out"
          style={{ transform: `translateX(-${currentBannerIndex * 100}%)` }}
        >
          {promotionBanners?.map((banner, index) => (
            <div key={index} className="w-full h-full flex-shrink-0">
              <img
                width={800}
                height={800}
                loading={currentBannerIndex === index ? 'eager' : 'lazy'}
                src={banner.url}
                alt={banner.title}
                className="w-full h-full object-cover"
              />
            </div>
          ))}
        </div>
        <div className="absolute left-0 right-0 bottom-4 flex justify-center space-x-2">
          {promotionBanners?.map((_, index) => (
            <button
              key={index}
              onClick={() => handleDotClick(index)}
              className={`w-3 h-3 rounded-full ${
                index === currentBannerIndex ? 'bg-dot' : 'bg-gray-400'
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default FeaturesPromotionSection;