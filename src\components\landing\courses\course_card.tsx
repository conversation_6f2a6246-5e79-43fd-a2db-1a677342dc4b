import { FaArrowUpLong } from 'react-icons/fa6';

type Props = {
  courseData: {
    id: number;
    title: string;
    description: string;
    subjects: {
      id: number;
      title: string;
      description: string;
      course_type: number;
    }[];
    attempts: {
      id: number;
      month: string;
      year: number;
    }[];
  };
  additionalClassNamesForCard?: string;
};

const CourseCard = ({ courseData, additionalClassNamesForCard }: Props) => {
  const titleParts = courseData.title.split(' ');
  const firstPart = titleParts[0];
  const remainingParts = titleParts.slice(1).join(' ');

  return (
    <div
      className={`bg-[#EEEEEE] min-w-[100px] p-3 lg:w-full mb-5 md:hover:bg-[#CFE0FF] cursor-pointer transition-all duration-150 flex flex-col-reverse gap-3 md:flex-row justify-end items-center md:justify-between md:p-4 rounded-lg shadow-md ${additionalClassNamesForCard} group`}
    >
      <div className=" w-full md:3/4 md:mr-0 md:pt-4 lg:pt-0 md:mt-0">
        <h2 className="text-[#1D1D1D] truncate font-primary font-medium text-[16px] leading-7 mb-0 flex flex-col">
          <span className="hidden md:block truncate">{courseData.title}</span>
          <span className="text-md md:hidden flex flex-col">
            <span className="w-full">{firstPart}</span>
            {remainingParts && (
              <span className=" truncate w-full">{remainingParts}</span>
            )}
          </span>
        </h2>
      </div>

      <div className="bg-[#FFFFFF] text-[#FFAE1E] group-hover:bg-[#2F50FF] group-hover:text-[white] cursor-pointer flex justify-center items-center rounded-full p-2 sm:p-3 md:ml-0 ml-auto">
        <FaArrowUpLong
          size={14}
          className="rotate-45 w-full h-full ease-in-out transition-all duration-200"
        />
      </div>
    </div>
  );
};

export default CourseCard;
