import { baseUrl } from '@/config/constants';
import CoursesSection from './CourseSection';

async function fetchCourses(course: string) {
  const res = await fetch(`${baseUrl}/courses/?course_group__name=${course}`, {
    method: 'GET',
    cache: 'no-store',
  });
  if (!res.ok) {
    throw new Error('Failed to fetch courses');
  }
  const data = await res.json();
  return data.results;
}

const Courses = async ({ course = '' }: { course?: string }) => {
  let courses = [];

  try {
    const data = await fetchCourses(course);
    courses = data;
  } catch (error) {
    console.error('Error fetching courses:', error);
  }

  return (
    <div className=" overscroll-x-none relative h-56">
      <CoursesSection coursesData={courses} />
    </div>
  );
};

export default Courses;
