import CoursePageClient from '@/components/navbar/course/CoursePage';
import { Metadata } from 'next';
import DownloadBanner from '@/components/landing/download/download_banner';
import Faculties from '@/components/landing/faculties/Faculties';
import FeaturesSection from '@/components/landing/features/FeaturesSection';
import HeroImageSection from '@/components/Promotion';
import TestSeriesSection from '@/components/landing/landing_test_series/TestSeries';
import HomePageSwiper from '@/components/landing/swiper/home_page_swiper';
import FilterModal from '@/components/test_series/filter_modal';
import WhatsAppIcon from '@/components/dashboard/whatsApp';
import FaqComponent from '@/components/landing/faq/courseFaq';
import BlogsCards from '@/components/blogs/blogsCard';
import MaharashtraTestSeries from '@/components/course/MaharashtraContent';

export async function generateMetadata(): Promise<Metadata> {
  let title = '';
  let description = '';
  let keywords = '';
  let url = '';
  let imageUrl = '';

  if (process.env.NEXT_PUBLIC_IS_PROD == 'true') {
    title = 'Maharashtra Board SSC & HSC Online Test Series | Gradehunt';
    description =
      'Prepare for Maharashtra Board SSC & HSC exams with Gradehunt’s online test series. Get latest pattern test papers, expert mentorship & instant doubt-solving.';
    (keywords =
      'Maharashtra Board Test Series, SSC Online Test Series, HSC Online Test Series, Maharashtra Class 10 Preparation, Class 12 Online Tests, Gradehunt Test Series, Maharashtra Board Practice Papers, Maharashtra Board 2025 Exams'),
      'Online Preparation for Maharashtra Board';
    url = 'https://gradehunt.com/course/Maharashtra-Board-test-series';
    imageUrl = 'https://gradehunt.com/icons/gradehunt_logo.svg';
  }

  return {
    title,
    description,
    keywords: keywords.split(', '),
    alternates: { canonical: url },
    openGraph: {
      title,
      description:
        'Crack your SSC & HSC exams with Gradehunt’s expert-curated online test series and mentorship. Based on latest Maharashtra Board exam pattern.',
      url,
      images: [imageUrl],
      type: 'website',
      locale: 'en_US',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description:
        'Get ready for Maharashtra Board Class 10 & 12 exams with expert guidance, test papers & instant doubt-solving.',
      images: [imageUrl],
      site: '@gradehunt',
      creator: '@gradehunt',
    },
    other: {
      rating: 'General',
      Category: 'Education',
      Language: 'en-US',
      distribution: 'global',
    },
  };
}

const CoursePage = () => {
  return (
    <div>
      <WhatsAppIcon />
      <FilterModal />
      <HeroImageSection course="boards" />
      <CoursePageClient courseName="maharashtra board" />
      <section className="px-[20px] py-8 lg:py-8 lg:px-[100px]">
        <TestSeriesSection course_type={[13, 14]} />
      </section>
      <FeaturesSection />
      <HomePageSwiper course="boards" course_name="Maharashtra Board" />
      <Faculties course="boards" />
      <BlogsCards category="" course="BOARD" />
      <DownloadBanner />
      <FaqComponent courseName="default" />
      <MaharashtraTestSeries />
    </div>
  );
};

export default CoursePage;
