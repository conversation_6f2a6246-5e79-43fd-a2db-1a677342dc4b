import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import SecondaryButton from '../ui/SecondaryButton';
import { useDispatch } from 'react-redux';
import { useCreateUserAuthMutation } from '@/redux/apiSlice';
import { updateUser } from '@/redux/features/userSlice';
import toast from 'react-hot-toast';
import { baseUrl } from '@/config/constants';

const CheckoutModal = ({
  isOpen,
  onClose,
  phoneNum,
  setPhoneNum,
  checkoutPhoneError,
  setIsVerifyingPhone,
  selectedTestSeries,
}: {
  isOpen: boolean;
  onClose: any;
  phoneNum: string;
  setPhoneNum: any;
  checkoutPhoneError: string;
  isVerifyingPhone: boolean;
  setIsVerifyingPhone: React.Dispatch<React.SetStateAction<boolean>>;
  selectedTestSeries: number;
}) => {
  const [createUserAuth] = useCreateUserAuthMutation();
  const dispatch = useDispatch();
  const [tempPhoneNum, setTempPhoneNum] = useState(phoneNum);
  const [shouldCloseModal, setShouldCloseModal] = useState(false);

  const handleGetAccessToken = async () => {
    const bodyData = {
      phone: tempPhoneNum,
    };

    const { data: accessRes } = await createUserAuth({ body: bodyData });
    console.log(accessRes);
    if (accessRes) {
      dispatch(
        updateUser({
          userId: accessRes.user_id,
          authTokens: {
            accessToken: accessRes.access,
            refreshToken: accessRes.refresh,
          },
        })
      );
    }
    return [accessRes.access, accessRes.user_id];
  };

  const handleNext = async () => {
    if (tempPhoneNum.length === 10) {
      setPhoneNum(tempPhoneNum);
      setIsVerifyingPhone(true);

      const [accessToken, userId] = await handleGetAccessToken();

      if (userId && selectedTestSeries !== 0) {
        fetch(`${baseUrl}/admin/purchase-intent-analytics/`, {
          method: 'POST',
          headers: {
            Authorization: `${accessToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            user_id: userId,
            test_series_id: selectedTestSeries,
          }),
        }).catch((error) => {
          console.error('Failed to track purchase intent:', error);
        });
      }

      setShouldCloseModal(true);
    } else {
      toast.error('10 digit Mobile Number Is Mandatory for purchase');
    }
  };
  useEffect(() => {
    if (shouldCloseModal) {
      onClose();
      setShouldCloseModal(false);
    }
  }, [shouldCloseModal, onClose]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-sm top-[25vh] lg:top-[50vh] mx-auto sm:rounded-lg rounded-none sm:p-6 p-4">
        <DialogHeader>
          <DialogTitle className="font-primary_medium text-[#181818] font-medium text-[22px] leading-7">
            Mobile Number
          </DialogTitle>
        </DialogHeader>

        <div className="flex mx-auto w-full">
          <span className="px-2 mr-2 py-3 bg-[#F7FAFF] h-[50px] border-[1px] border-[#3F3F3F] rounded-sm">
            +91
          </span>
          <input
            inputMode="numeric"
            type="number"
            placeholder="Enter your phone number"
            value={tempPhoneNum}
            onChange={(e) => {
              if (e.target.value.length <= 10) {
                setTempPhoneNum(e.target.value);
              }
            }}
            className="border-[1px] border-[#112C79] bg-[#F7FAFF] rounded-sm p-4 w-full h-[50px]"
          />
        </div>

        <DialogFooter className="sm:justify-start w-full">
          <div onClick={handleNext} className="w-full">
            <SecondaryButton additionalClassNames="w-full">
              Next
            </SecondaryButton>
          </div>
        </DialogFooter>

        {checkoutPhoneError.length !== 0 && (
          <p className="text-red-500 font-primary_medium text-center text-lg">
            {checkoutPhoneError}
          </p>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default CheckoutModal;
