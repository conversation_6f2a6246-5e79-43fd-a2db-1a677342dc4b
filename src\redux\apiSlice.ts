import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { CourseData } from '@/types/Course';
import {
  NotesResponse,
  PurchasedTests,
  TestSeriesType,
  TestStatusData,
  TestSubmissionData,
} from '@/types/Test';
import { baseUrl } from '@/config/constants.ts';

export type GetCoursesResponse = {
  count: number;
  next: any;
  previous: any;
  results: CourseData[];
};

export type GetTestPaperResponse = {
  count: number;
  next: any;
  previous: any;
  results: TestStatusData[];
};

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl,
  }),
  endpoints: (builder) => ({
    getCourses: builder.query<GetCoursesResponse, string | void>({
      query: (course_group_name) =>
        course_group_name
          ? `/courses/?course_group__name=${course_group_name}`
          : `/courses/`,
    }),
    getTestSeriesByFilter: builder.query<
      TestSeriesType,
      { courseTypeId: number; attemptsIds: string }
    >({
      query: ({ courseTypeId, attemptsIds }) => ({
        method: 'GET',
        url: `/testseries/filter_by_course_and_attempts/?course_type=${courseTypeId}&attempts=${attemptsIds}`,
      }),
    }),
    createUserAuth: builder.mutation<any, { body: any }>({
      query: ({ body }) => ({
        url: '/client-auth/',
        method: 'POST',
        // headers: {
        //     'Authorization': `${headerToken}`,
        // },
        body,
      }),
    }),
    getPurchasedTests: builder.query<
      PurchasedTests,
      { headerToken: string; firebaseuid: string }
    >({
      query: ({ headerToken, firebaseuid }) => ({
        url: '/purchases',
        method: 'GET',
        headers: {
          Authorization: `${headerToken}`,
          UID: `${firebaseuid}`,
        },
      }),
    }),
    getNotes: builder.query<NotesResponse, { headerToken: string }>({
      query: ({ headerToken }) => ({
        url: '/notes',
        method: 'GET',
        headers: {
          Authorization: `${headerToken}`,
        },
      }),
    }),
    getReferals: builder.query<any, { headerToken: string; userId: string }>({
      query: ({ headerToken, userId }) => ({
        url: `/referral-profile/get_referral_profile/?user_id=${userId}`,
        method: 'GET',
        headers: {
          Authorization: `${headerToken}`,
        },
      }),
    }),
    getReferalsHistory: builder.query<
      any,
      { headerToken: string; referrer: number }
    >({
      query: ({ headerToken, referrer }) => ({
        url: `/referral-transaction/?referrer=${referrer}`,
        method: 'GET',
        headers: {
          Authorization: `${headerToken}`,
        },
      }),
    }),
    getReferalsPayoutRequest: builder.query<
      any,
      { headerToken: string; referrer: number }
    >({
      query: ({ headerToken, referrer }) => ({
        url: `/referral-payout-request/?referrer=${referrer}`,
        method: 'GET',
        headers: {
          Authorization: `${headerToken}`,
        },
      }),
    }),
    updateUserVpa: builder.mutation<
      any,
      { headerToken: string; vpa: string; id: number }
    >({
      query: ({ headerToken, vpa, id }) => ({
        url: `/referral-profile/${id}/`,
        method: 'PATCH',
        headers: {
          Authorization: `${headerToken}`,
        },
        body: { vpa },
      }),
    }),
    requestReferralPayout: builder.mutation<
      any,
      { headerToken: string; id: number; bal_amnt: number }
    >({
      query: ({ headerToken, id, bal_amnt }) => ({
        url: '/referral-payout-request/',
        method: 'POST',
        headers: {
          Authorization: `${headerToken}`,
        },
        body: {
          referrer_id: id,
          amount: bal_amnt,
        },
      }),
    }),
    getTestPaperSubject: builder.query<
      GetTestPaperResponse,
      { headerToken: string }
    >({
      query: ({ headerToken }) => ({
        url: '/test/status',
        method: 'GET',
        headers: {
          Authorization: `${headerToken}`,
        },
      }),
    }),
    createSubmission: builder.mutation<any, { headerToken: string; body: any }>(
      {
        query: ({ body, headerToken }) => ({
          url: '/submission/',
          method: 'POST',
          headers: {
            Authorization: `${headerToken}`,
          },
          body,
        }),
      }
    ),
    getSubmissions: builder.query<TestSubmissionData, { headerToken: string }>({
      query: ({ headerToken }) => ({
        url: '/submission/',
        method: 'GET',
        headers: {
          Authorization: `${headerToken}`,
        },
      }),
    }),
    createDoubt: builder.mutation<any, { headerToken: string; body: any }>({
      query: ({ body, headerToken }) => ({
        url: '/doubt/',
        method: 'POST',
        headers: {
          Authorization: `${headerToken}`,
        },
        body,
      }),
    }),
    updateUserDetails: builder.mutation<
      any,
      Partial<{
        phone?: string;
        email?: string;
        name?: string;
        gender?: string;
        firebase_uid?: string;
      }> & { userId: string; headerToken: string }
    >({
      query: ({ userId, headerToken, ...patchData }) => ({
        url: `/users/${userId}/`,
        method: 'PATCH',
        headers: {
          Authorization: `${headerToken}`,
        },
        body: patchData,
      }),
    }),
    getUserDetails: builder.query<any, { headerToken: string; userId: string }>(
      {
        query: ({ headerToken, userId }) => ({
          url: `/users/${userId}`,
          method: 'GET',
          headers: {
            Authorization: `${headerToken}`,
          },
        }),
      }
    ),
  }),
});

export const {
  useGetCoursesQuery,
  useGetTestSeriesByFilterQuery,
  useCreateUserAuthMutation,
  useGetPurchasedTestsQuery,
  useGetNotesQuery,
  useGetReferalsQuery,
  useGetReferalsHistoryQuery,
  useGetReferalsPayoutRequestQuery,
  useUpdateUserVpaMutation,
  useRequestReferralPayoutMutation,
  useGetTestPaperSubjectQuery,
  useCreateSubmissionMutation,
  useGetSubmissionsQuery,
  useCreateDoubtMutation,
  useUpdateUserDetailsMutation,
  useGetUserDetailsQuery,
} = apiSlice;
