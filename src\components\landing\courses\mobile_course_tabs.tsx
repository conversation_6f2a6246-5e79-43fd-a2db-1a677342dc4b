'use client';

import { Tabs, TabsList } from '@/components/ui/tabs';
import CourseColumn from './course_column';
import { CourseData } from '@/types/Course';
import { usePathname } from 'next/navigation';

type CourseSectionProps = {
  coursesData: CourseData[] | undefined;
  activeTabName?: string | null;
};

export function MobileCourseTabs({
  coursesData,
  activeTabName,
}: CourseSectionProps) {
  const pathname = usePathname();
  const isAboutPage = pathname === '/about-us' || pathname === '/our-features';

  // Extract unique course names
  const courseNames = [...new Set(coursesData?.map((course) => course.name))];
  return (
    <Tabs
      defaultValue={activeTabName ? activeTabName : courseNames[0]}
      className={`${isAboutPage && 'mb-52'} w-full lg:hidden block`}
    >
      {/* <h2 className="ml-2 font-primary_medium text-2xl">Courses</h2> */}
      <TabsList
        className={`bg-transparent sm:flex relative my-10 ${
          isAboutPage && 'grid grid-cols-3'
        }`}
      >
        {coursesData?.map((course, index) => (
          <CourseColumn key={index} course={course} />
        ))}
      </TabsList>
      {/* <hr className="my-2 text-[#E3E3E3] w-[inherit]" /> */}

      {/* {courseNames.map((name) => (
        <TabsContent key={name} value={name}>
          <div className="lg:hidden flex w-full flex-wrap">
            {coursesData
              ?.filter((course) => course.name === name)
              .map((course, index) => (
                <CourseColumn key={index} course={course} />
              ))}
          </div>
        </TabsContent>
      ))} */}
    </Tabs>
  );
}
