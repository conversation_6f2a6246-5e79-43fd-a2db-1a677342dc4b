import CoursesSection from '@/components/landing/courses/courses';
import HomePageSwiper from '@/components/landing/swiper/home_page_swiper';
import FeaturesSection from '@/components/landing/features/FeaturesSection';
import DownloadBanner from '@/components/landing/download/download_banner';
import FAQSection from '@/components/landing/faq/faq_section';
import TestSeriesSection from '@/components/landing/landing_test_series/TestSeries';
import FilterModal from '@/components/test_series/filter_modal';
import WhatsAppIcon from '@/components/dashboard/whatsApp';
import HeroImageSection from '@/components/Promotion';
import Faculties from '@/components/landing/faculties/Faculties';
import BlogsCards from '@/components/blogs/blogsCard';

const HeroSection = async () => {
  return (
    <div className="overflow-x-hidden relative">
      <FilterModal />
      <HeroImageSection course="professional" />
      <CoursesSection course="professional" />
      <div className="px-[20px] py-8 lg:pb-8 lg:px-[100px]">
        <TestSeriesSection course="professional" />
      </div>
      <FeaturesSection />
      <HomePageSwiper course="professional" />
      <div id="faculties">
        <Faculties course="professional" />
      </div>
      <BlogsCards category="" course="PROFESSIONAL" />
      <DownloadBanner />
      <div id="faqs">
        <FAQSection ids={[7, 8, 9]} />
      </div>
      <WhatsAppIcon />
    </div>
  );
};

export default HeroSection;
