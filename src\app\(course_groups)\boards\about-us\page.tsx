'use client';

import FilterModal from '@/components/test_series/filter_modal';
import { useGetCoursesQuery } from '@/redux/apiSlice';
import { useRouter } from 'next/navigation';
import CourseColumn from '@/components/landing/courses/course_column';
import { MobileCourseTabs } from '@/components/landing/courses/mobile_course_tabs';
import TestSeriesSectionClient from '@/components/landing/landing_test_series/TestSeriesSectionClient';

const AboutUsPage = () => {
  const {
    data: coursesData,
    error,
    isLoading,
  } = useGetCoursesQuery('boards', { refetchOnMountOrArgChange: true });

  const router = useRouter();

  if (isLoading) {
    return <div>Loading courses...</div>;
  }

  if (error) {
    return <div>Error loading courses</div>;
  }

  return (
    <section className="lg:px-[100px] px-[20px] py-10 space-y-8 lg:space-y-16">
      <div className="space-y-8">
        <div className="flex gap-4 items-center">
          <img
            loading="lazy"
            className="cursor-pointer"
            onClick={() => router.back()}
            src="/icons/back.svg"
            alt="back"
          />
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-primary_medium font-medium text-[#1D1D1D]">
            How it works
          </h2>
        </div>
        <FilterModal />
        <div>
          <img
            loading="lazy"
            src="/images/about_us_flow.svg"
            className="lg:block hidden pl-0 ml-0 w-full"
          />
          <img
            loading="lazy"
            src="/images/about_us_flow_mobile.svg"
            className="mx-auto sm:w-[40%] lg:hidden my-8 block"
          />
        </div>
      </div>
      <div>
        <h2 className="hidden lg:block font-primary_medium text-2xl md:text-3xl lg:text-4xl my-4">
          Courses
        </h2>
        <div className={`lg:grid grid-cols-3 gap-x-8 hidden`}>
          {coursesData?.results.map((course, index) => (
            <CourseColumn
              additionalClassNamesForCard=""
              course={course}
              key={index}
            />
          ))}
        </div>
      </div>
      <div className="my-8">
        <MobileCourseTabs coursesData={coursesData?.results} />
      </div>
      <TestSeriesSectionClient course="boards" />
    </section>
  );
};

export default AboutUsPage;
