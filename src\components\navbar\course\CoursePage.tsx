'use client';

import { useEffect, useState } from 'react';
import { useGetCoursesQuery } from '@/redux/apiSlice';
import CourseColumn from '@/components/landing/courses/course_column';
import { MobileCourseTabs } from '@/components/landing/courses/mobile_course_tabs';
import FilterModal from '@/components/test_series/filter_modal';

interface CoursePageProps {
  courseName: string;
}

function CoursePageClient({ courseName }: CoursePageProps) {
  const { data: coursesData } = useGetCoursesQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });
  const courseDataFiltered =
    coursesData?.results.filter(
      (item) => item.name.toLowerCase() == courseName
    ) || [];
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return null;
  }

  return (
    <section className="px-[20px] py-12 md:py-0 lg:px-[100px]">
      <FilterModal />
      <div className="flex-col lg:flex hidden gap-12">
        {coursesData ? (
          <div className="flex-col mt-20 md:mt-0 lg:flex hidden gap-12">
            {courseDataFiltered.map((course, index) => (
              <CourseColumn course={course} key={index} />
            ))}
          </div>
        ) : (
          <div>Loading...</div>
        )}
      </div>
      <MobileCourseTabs
        activeTabName={courseName.toUpperCase()}
        coursesData={courseDataFiltered || undefined}
      />
    </section>
  );
}

export default CoursePageClient;
