import { BsDot } from 'react-icons/bs';
import TestSeriesButton from '@/components/ui/TestSeriesButton';
import React from 'react';
import { setSelectedTestSeries } from '@/redux/features/testSeriesSlice.ts';
import { TestSeriesType } from '@/types/Test.ts';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
import { FaChevronRight } from 'react-icons/fa6';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/redux/store.ts';
import { baseUrl } from '@/config/constants';

const TestSeriesCard = ({
  title,
  test,
}: {
  title: string;
  test: TestSeriesType;
}) => {
  const itemsArray = test.test_series_type.description
    .split(',')
    .map((item) => {
      return { content: item.trim() };
    });
  // itemsArray.push({
  //     content : `Valid From - ${test.valid_from}`,
  // }, { content : `Valid Till - ${test.valid_till}` })

  const dispatch = useDispatch();
  const router = useRouter();
  const currentUser = useSelector((state: RootState) => state.user);

  const handleNavigateSubject = (test: TestSeriesType) => {
    if (currentUser?.userId) {
      fetch(`${baseUrl}/admin/purchase-intent-analytics/`, {
        method: 'POST',
        headers: {
          'Authorization': `${currentUser.authTokens.accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: currentUser.userId,
          test_series_id: test.id,
        }),
      }).catch((error) => {
        console.error('Failed to track purchase intent:', error);
      });
    }

    dispatch(setSelectedTestSeries(test));
    router.push(`/subject`);
  };
  return (
    <div className="bg-[#F2F2F2] p-4 lg:p-10 flex h-full flex-col lg:gap-10 gap-5 rounded-xl w-full">
      <h2 className="font-primary_medium pl-4 lg:px-0 text-[22px] lg:text-2xl text-[#1D1D1D]">
        {title}
      </h2>

      <div className="flex flex-col lg:gap-2 gap-1">
        {itemsArray.map((item, index) => (
          <React.Fragment key={index}>
            <p className="text-[#1C1B1F] max-h-[17px] lg:max-h-[20px] flex items-center justify-start gap-0 text-sm">
              <BsDot size={44} color="#FFAE1E" /> {item.content}
            </p>
            <hr className="max-w-[inherit] mx-3 border-[#CAC4D0] border-[1px] mb-2 lg:mb-3" />
          </React.Fragment>
        ))}
      </div>

      <div className="flex flex-row items-center mt-auto gap-2 lg:px-0">
        <button
          onClick={() => {
            if (
              !test ||
              !test.fees_structure ||
              test.fees_structure.length === 0
            ) {
              toast.error('Not Uploaded By Gradehunt');
            } else {
              window.open(test.fees_structure, '_blank');
            }
          }}
        >
          <TestSeriesButton content="Fee Structure" />
        </button>

        <button
          onClick={() => {
            if (!test || !test.syllabus || test.syllabus.length === 0) {
              toast.error('Not Uploaded By Gradehunt');
            } else {
              window.open(test.syllabus, '_blank');
            }
          }}
        >
          <TestSeriesButton content="Syllabus" />
        </button>
        <button
          className="ml-auto text-white bg-gray-800 rounded-full py-2 px-6 font-medium text-xs md:text-base flex items-center justify-center hover:bg-black transition-colors duration-200 ease-in-out w-max self-start"
          onClick={() => handleNavigateSubject(test)}
        >
          Buy Now
          <FaChevronRight className="ml-2" color="white" size={14} />
        </button>
      </div>
    </div>
  );
};

export default TestSeriesCard;
