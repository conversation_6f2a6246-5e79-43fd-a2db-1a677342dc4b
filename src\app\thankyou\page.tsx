'use client';

import SecondaryButton from '@/components/ui/SecondaryButton';
import { useRouter } from 'next/navigation';

const ThankYouPage = () => {
  const router = useRouter();
  return (
    <div className="flex justify-center flex-col my-auto min-h-screen w-full items-center">
      <img loading="lazy" src="/images/thankyou.svg" alt="Thank You" />
      <div className="flex justify-center flex-col gap-2">
        <p className="text-[#1D1D1D] text-center font-primary_medium text-[22px] leading-7">
          Thank You!
        </p>
        <p className="text-[#1D1D1D] text-center font-primary font-normal text-base leading-5">
          Your payment was done successfully.
        </p>
        <div
          onClick={() => router.push('/dashboard')}
          className="mt-4 cursor-pointer w-full"
        >
          <SecondaryButton additionalClassNames="w-full">
            Go to dashboard
          </SecondaryButton>
        </div>
      </div>
    </div>
  );
};

export default ThankYouPage;
