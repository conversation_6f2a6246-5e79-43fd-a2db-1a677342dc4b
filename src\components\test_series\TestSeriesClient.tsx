'use client';

import { useEffect, useState } from 'react';
import TestSeriesCard from '@/components/test_series/test_series_card';
import { IoFilterSharp } from 'react-icons/io5';
import FilterModal from '@/components/test_series/filter_modal';
import { openModal } from '@/redux/features/filterModalSlice.ts';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/redux/store.ts';
import { TestSeriesType } from '@/types/Test.ts';
import { baseUrl } from '@/config/constants';
import { useSearchParams, useRouter } from 'next/navigation';

const TestSeriesClient = () => {
  const dispatch = useDispatch();
  const isOpen = useSelector((state: RootState) => state.filterModal.isOpen);
  const selectedMainCourse = useSelector(
    (state: RootState) => state.course.selectedMainCourse
  );
  const selectedCourseType = useSelector(
    (state: RootState) => state.course.selectedCourseType
  );
  const router = useRouter();
  const searchParams = useSearchParams(); // Use useSearchParams hook
  const [testSeriesData, setTestSeriesData] = useState<TestSeriesType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filteredTestSeries, setFilteredTestSeries] = useState<
    TestSeriesType[]
  >([]);

  useEffect(() => {
    const fetchTestSeries = async () => {
      setLoading(true);
      try {
        // Retrieve query parameters from useSearchParams
        const courseTypeId = parseInt(
          searchParams.get('coursetypeid') || '0',
          10
        );
        const attempts = searchParams.get('attemptid') || '';

        const response = await fetch(
          `${baseUrl}/testseries/filter_by_course_and_attempts/?course_type=${courseTypeId}&attempts=${attempts}`
        );
        if (!response.ok) {
          throw new Error('Failed to fetch data');
        }
        const data = await response.json();
        
        setTestSeriesData(data);
      } catch (error: any) {
        setError(error.message || 'Failed to fetch data');
      } finally {
        setLoading(false);
      }
    };

    fetchTestSeries();
  }, [searchParams]);

  useEffect(() => {
    if (!selectedMainCourse) {
      router.push('/');
    }
  }, [router, selectedMainCourse]);

  const handleOpenModal = () => {
    dispatch(openModal());
  };

  const checkExpiry = (validTill: string) => {
    const [year, month, day] = validTill.split('-').map(Number);
    const formattedDate = new Date(year, month - 1, day);
    const currentDate = new Date();
    return formattedDate < currentDate;
  };

  useEffect(() => {
    const filteredData: TestSeriesType[] = testSeriesData.filter(
      (series) => !checkExpiry(series.valid_till)
    );
    setFilteredTestSeries(filteredData || []);
  }, [testSeriesData]);

  return (
    <section className="lg:px-[100px] px-[20px] py-10">
      <FilterModal isOpen={isOpen} />
      <div className="flex items-start lg:justify-between mb-4 mx-auto w-full gap-4">
        <div className="flex gap-4 items-center">
          <img
            loading="lazy"
            className="cursor-pointer"
            onClick={() => router.back()}
            src="/icons/back.svg"
            alt="back"
          />
          <h2 className="font-primary_medium lg:block hidden text-base text-[#1D1D1D]">
            Test Series
          </h2>
        </div>
        <div className="flex flex-row w-full justify-between lg:w-fit lg:gap-12">
          {selectedCourseType?.title}
          <div
            className="text-[#2F50FF] cursor-pointer"
            onClick={handleOpenModal}
          >
            <IoFilterSharp size={24} />
          </div>
        </div>
      </div>

      {loading ? (
        <p>Loading...</p>
      ) : error ? (
        <p>{error}</p>
      ) : filteredTestSeries.length !== 0 ? (
        <div className="grid lg:grid-cols-2 grid-cols-1 gap-6 max-w-7xl mx-auto">
          {filteredTestSeries.map(
            (test, index) =>
              !checkExpiry(test.valid_till) && (
                <div className="" key={index}>
                  <TestSeriesCard title={test.title} test={test} />
                  {/* Render other card details here */}
                </div>
              )
          )}
        </div>
      ) : (
        <p className="text-yellow-600 mt-10 text-center text-lg font-primary_medium">
          No Test Series Found!
        </p>
      )}
    </section>
  );
};

export default TestSeriesClient;
