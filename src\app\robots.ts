import { robots_visible } from '@/config/constants'
import type { MetadataRoute } from 'next'
 
export default function robots(): MetadataRoute.Robots {

    if(robots_visible){
        if(process.env.NEXT_PUBLIC_IS_PROD == "true"){
          return {
            rules: {
              userAgent: '*',
              disallow: '/private/',
              allow: '/',
            },
            sitemap: 'https://gradehunt.com/sitemap.xml',
          }
        }
        return {
          rules: {
            userAgent: '*',
            disallow: '/private/',
            allow: '/',
          }
        }
    }
    return {
        rules: {
            userAgent: '*',
            disallow: '/',
          },
    }
}