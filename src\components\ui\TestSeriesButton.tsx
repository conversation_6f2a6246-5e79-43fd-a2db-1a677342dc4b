import React from 'react';

const TestSeriesButton = ({ content, icon } : { content: string, icon?: React.ReactNode }) => {
  return (
    <button className="sm:px-6 sm:py-2 px-3 py-0.5 rounded-[100px] border-[1px] border-[#2F50FF] text-[#2F50FF] font-primary_medium text-xs md:text-base text-center 
    hover:bg-[#2F50FF] hover:text-[#FFFFFF] flex items-center gap-2 hover:border-[#FFFFFF] transition-all duration-150
    ">
      {content}
      {icon && <span className="mr-2">{icon}</span>} {/* Conditionally render the icon if it is provided */}
    </button>
  )
}

export default TestSeriesButton;
