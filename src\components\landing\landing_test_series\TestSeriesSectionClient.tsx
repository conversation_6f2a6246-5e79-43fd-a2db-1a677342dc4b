'use client';

import { useEffect, useState } from 'react';
import LandingTestSeries from './landing_test_series';
import { baseUrl } from '@/config/constants';

type TestSeriesProps = {
  course?: string;
};

const TestSeriesSectionClient = ({ course = '' }: TestSeriesProps) => {
  const [testSeries, setTestSeries] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTestSeries = async () => {
      try {
        const res = await fetch(
          `${baseUrl}/testseries-type?course_type__course_group__name=${course}`,
          {
            cache: 'no-store',
          }
        );
        if (!res.ok) {
          throw new Error('Failed to fetch test series');
        }
        const data = await res.json();
        setTestSeries(data);
      } catch (err) {
        console.error('Error fetching test series:', err);
        setError('Failed to load test series.');
      } finally {
        setLoading(false);
      }
    };

    fetchTestSeries();
  }, []);

  if (loading) {
    return <div>Loading test series...</div>;
  }

  if (error) {
    return <div>{error}</div>;
  }

  return (
    <div className="overflow-x-hidden relative ">
      <LandingTestSeries allTestSeries={testSeries} />
    </div>
  );
};

export default TestSeriesSectionClient;
