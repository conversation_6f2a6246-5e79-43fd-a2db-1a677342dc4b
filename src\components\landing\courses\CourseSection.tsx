import { CourseData } from '@/types/Course';
import CourseColumn from './course_column';
import { MobileCourseTabs } from './mobile_course_tabs';

interface CoursesSectionProps {
  coursesData: CourseData[];
}

const CoursesSection: React.FC<CoursesSectionProps> = ({ coursesData }) => {
  return (
    <section className="px-[20px] lg:py-16 lg:pb-8 lg:px-[100px]">
      <h2 className="lg:block font-primary_medium text-2xl md:text-3xl lg:text-4xl my-4">
        Courses
      </h2>
      <div className={`lg:grid grid-cols-3 gap-x-8 gap-y-4 hidden`}>
        {coursesData.map((course, index) => {
          return (
            <CourseColumn
              additionalClassNamesForCard=""
              course={course}
              key={index}
            />
          );
        })}
      </div>
      <MobileCourseTabs coursesData={coursesData} />
    </section>
  );
};

export default CoursesSection;
