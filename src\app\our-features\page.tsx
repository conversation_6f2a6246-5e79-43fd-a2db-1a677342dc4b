'use client';

import { useRouter } from 'next/navigation';
import OurUniqueFeatureCard from '@/components/about_us/our_unique_feature_card';
import { useGetCoursesQuery } from '@/redux/apiSlice';
import CourseColumn from '@/components/landing/courses/course_column';
import { MobileCourseTabs } from '@/components/landing/courses/mobile_course_tabs';
import FilterModal from '@/components/test_series/filter_modal';
import TestSeriesSectionClient from '@/components/landing/landing_test_series/TestSeriesSectionClient';

const OurUniqueFeatures = () => {
  const router = useRouter();
  const { data: coursesData } = useGetCoursesQuery(undefined,{refetchOnMountOrArgChange: true});;

  const uniqueFeatures = [
    {
      title: 'Evaluation in detail',
      description:
        'At Gradehunt, test papers are evaluated in a comprehensive manner by experts CAs with 7+ years experience.',
    },
    {
      title: 'Score on Presentation',
      description:
        'Presentation is the most important factor which will boost at least 10-12 marks in each exam. So we give a detailed score on presentation and ways to improve it.',
    },
    {
      title: 'Detailed Comments',
      description:
        'We give comments and suggestions on each and every part of the question, so that the same mistake is not repeated in the final exam.',
    },
    {
      title: 'Stepwise Marking',
      description:
        'In CA, CS and CMA Professional exams, stepwise marking is done. We also follow the same approach, and suggested answers are also given with a stepwise marking scheme.',
    },
    {
      title: 'Doubt solving support',
      description:
        'For each test attempted, you can ask doubts in evaluation and connect with the faculty; we answer all your doubts in 24 hours.',
    },
    {
      title: 'Quick Evaluation',
      description:
        'Our team efficiently checks all the answer sheets in less than 48 hours. Speedy checking of sheets further helps a student improve performance and learn from mistakes.',
    },
    {
      title: 'All India Ranking',
      description:
        'Providing a rank for each test helps a student to know where they stand among their peers and motivates them to work even harder.',
    },
    {
      title: 'Topper’s Sheet',
      description:
        'For each test written, the student who scores the highest marks has their sheet shared with all the students writing that test, providing an opportunity for collective learning and improvement.',
    },
    {
      title: 'Important Notes and MCQ',
      description:
        'We share subject-wise notes and MCQ for all the subjects, so that students can get all the material under one roof, eliminating the need to scroll through telegram and social media searching for notes and material.',
    },
    {
      title: 'Mentorship Program',
      description:
        'We also provide a mentorship program, where you can interact on a one-on-one basis with expert Chartered Accountants throughout your CA exam journey.',
    },
  ];

  return (
    <section className="lg:px-[100px] px-[20px] py-10 space-y-8 lg:space-y-16">
      <FilterModal />
      <div className="space-y-8">
        <div className="flex gap-4 items-center">
          <img
            loading="lazy"
            className="cursor-pointer"
            onClick={() => router.back()}
            src="/icons/back.svg"
            alt="back"
          />
          <h2 className="text-[#1D1D1D] text-xl md:text-2xl lg:text-3xl font-primary_medium leading-7">
            Our Unique Features
          </h2>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mt-10 lg:mt-2">
          {uniqueFeatures.map((feature, index) => (
            <OurUniqueFeatureCard
              cardNum={index + 1}
              title={feature.title}
              desc={feature.description}
            />
          ))}
        </div>
      </div>
      <div>
        <h2 className="hidden lg:block font-primary_medium text-2xl md:text-3xl lg:text-4xl my-4">
          Courses
        </h2>
        <div className={`lg:grid grid-cols-3 gap-x-8 hidden`}>
          {coursesData?.results.map((course, index) => {
            return (
              <CourseColumn
                additionalClassNamesForCard=""
                course={course}
                key={index}
              />
            );
          })}
        </div>
      </div>
      <div className="my-8">
        <MobileCourseTabs coursesData={coursesData?.results} />
      </div>
      <TestSeriesSectionClient />
    </section>
  );
};

export default OurUniqueFeatures;
