import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { CourseData } from '@/types/Course';

interface CoursePageState {
    selectedCoursePage : CourseData[] | null
}

const initialState: CoursePageState = {
    selectedCoursePage : null
};

const coursePageSlice = createSlice({
    name : 'coursepage',
    initialState,
    reducers : {
        setSelectedCoursePage : (state,action : PayloadAction<CourseData[] | null>) => {
            state.selectedCoursePage = action.payload
        },
        clearSelectedCoursePage : (state) => {
            state.selectedCoursePage = null;
        }

    }
})


export const { setSelectedCoursePage,clearSelectedCoursePage } = coursePageSlice.actions;
export default coursePageSlice.reducer;