import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getAuth } from "firebase/auth";
import { firebase_api_key, firebase_app_id, firebase_auth_domain, firebase_measurement_id, firebase_messaging_id, firebase_project_id, firebase_storage_bucket } from "@/config/constants";
import {initializeAppCheck, ReCaptchaV3Provider } from "firebase/app-check";

const firebaseConfig = {
    apiKey: firebase_api_key,
    authDomain: firebase_auth_domain,
    projectId: firebase_project_id,
    storageBucket: firebase_storage_bucket,
    messagingSenderId: firebase_messaging_id,
    appId: firebase_app_id,
    measurementId: firebase_measurement_id
};


export const app = initializeApp(firebaseConfig);

export const appCheck = initializeAppCheck(app, {
    provider: new ReCaptchaV3Provider('6Lf_NEoqAAAAADkOn2N8u3L1ykLgVbkNReHp5rlS'),
  
    // Optional argument. If true, the SDK automatically refreshes App Check
    // tokens as needed.
    isTokenAutoRefreshEnabled: true
  });
  
export const analytics = getAnalytics(app);
export const auth = getAuth(app);