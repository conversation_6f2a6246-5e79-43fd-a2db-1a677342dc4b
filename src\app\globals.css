@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: "Helvetica-Regular";
  src: local("Helvetica-Regular"),
  url('../../fonts/Helvetica.ttf') format('truetype');
}

@font-face {
  font-family: "Helvetica-Medium";
  src: local("Helvetica-Medum"),
  url('../../fonts/helvetica-medium.ttf') format('truetype');
}


  @layer base {
    :root {
      --background: 0 0% 100%;
      --foreground: 222.2 84% 4.9%;

      --card: 0 0% 100%;
      --card-foreground: 222.2 84% 4.9%;

      --popover: 0 0% 100%;
      --popover-foreground: 222.2 84% 4.9%;

      --primary: 222.2 47.4% 11.2%;
      --primary-foreground: 210 40% 98%;

      --secondary: 210 40% 96.1%;
      --secondary-foreground: 222.2 47.4% 11.2%;

      --muted: 210 40% 96.1%;
      --muted-foreground: 215.4 16.3% 46.9%;

      --accent: 210 40% 96.1%;
      --accent-foreground: 222.2 47.4% 11.2%;

      --destructive: 0 84.2% 60.2%;
      --destructive-foreground: 210 40% 98%;

      --border: 214.3 31.8% 91.4%;
      --input: 214.3 31.8% 91.4%;
      --ring: 222.2 84% 4.9%;

      --radius: 0.5rem;
    }

    .dark {
      --background: 222.2 84% 4.9%;
      --foreground: 210 40% 98%;

      --card: 222.2 84% 4.9%;
      --card-foreground: 210 40% 98%;

      --popover: 222.2 84% 4.9%;
      --popover-foreground: 210 40% 98%;

      --primary: 210 40% 98%;
      --primary-foreground: 222.2 47.4% 11.2%;

      --secondary: 217.2 32.6% 17.5%;
      --secondary-foreground: 210 40% 98%;

      --muted: 217.2 32.6% 17.5%;
      --muted-foreground: 215 20.2% 65.1%;

      --accent: 217.2 32.6% 17.5%;
      --accent-foreground: 210 40% 98%;

      --destructive: 0 62.8% 30.6%;
      --destructive-foreground: 210 40% 98%;

      --border: 217.2 32.6% 17.5%;
      --input: 217.2 32.6% 17.5%;
      --ring: 212.7 26.8% 83.9%;
    }
  }

  @layer base {
    * {
      @apply border-border;
    }
    body {
      @apply bg-background text-foreground;
    }
    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
    .blog-parent a {
      color: blue;
    }
  }

/* Custom Swiper styles */
.blogs-swiper-container {
  padding: 0 40px;
  position: relative;
}

.blogs-swiper-prev,
.blogs-swiper-next {
  color: #4f46e5;
  background: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  z-index: 10;
  transition: opacity 0.3s ease;
}

.blogs-swiper-prev {
  left: -6px;
}

.blogs-swiper-next {
  right: -6px;
}

.blogs-swiper-prev::after,
.blogs-swiper-next::after {
  font-size: 18px;
  font-family: 'swiper-icons';
}

.blogs-swiper-prev::after {
  content: 'prev';
}

.blogs-swiper-next::after {
  content: 'next';
}

.blogs-swiper-prev.swiper-button-disabled,
.blogs-swiper-next.swiper-button-disabled {
  opacity: 0.35;
  cursor: auto;
  pointer-events: none;
}

.swiper-pagination-bullet-active {
  background: #4f46e5 !important;
}