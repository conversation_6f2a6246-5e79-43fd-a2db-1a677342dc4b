import React from 'react';
import { baseUrl } from '@/config/constants';
import NotFoundPage from '../not-found/page';
import CoursePageClient from '@/components/navbar/course/CoursePage';
import TestSeriesSection from '@/components/landing/landing_test_series/TestSeries';
import FeaturesSection from '@/components/landing/features/FeaturesSection';
import HomePageSwiper from '@/components/landing/swiper/home_page_swiper';
import Faculties from '@/components/landing/faculties/Faculties';
import FeatureSubSection from '@/components/blogs/FeatureSubsection';
import WhatsAppIcon from '@/components/dashboard/whatsApp';
import FeaturesPromotionSection from '@/components/landing/FeaturesPromotionSection';
import { parseWithBR } from '@/lib/utils';

// ISR: Revalidate the page every 60 seconds
export const revalidate = 60;

// Generate static metadata for each page
export async function generateMetadata({
  params,
}: {
  params: { slug: string };
}) {
  // Fetch all features to find the correct blog by slug
  const response = await fetch(
    `${baseUrl}/z/blogs/pages/?type=blogs.FeaturePage&limit=1000`,
    {
      next: { revalidate: 60 },
    }
  );
  const features = await response.json();
  const feature = features.items?.find(
    (item: any) => item.meta.slug === params.slug
  );

  if (!feature) {
    return {
      title: 'Not Found - Gradehunt',
      description: 'The requested blog could not be found.',
    };
  }

  // Fetch detailed metadata for the blog
  const detailResponse = await fetch(feature.meta.detail_url, {
    next: { revalidate: 60 },
  });
  const featureDetail = await detailResponse.json();

  return {
    title: `${featureDetail.meta.seo_title}`,
    description: featureDetail.meta.search_description,
    keywords: [],
    alternates: {
      canonical: `https://gradehunt.com/${featureDetail.meta.slug}`,
    },
    openGraph: {
      type: 'website',
      locale: 'en_US',
      url: `https://gradehunt.com/${featureDetail.meta.slug}`,
      title: `${featureDetail.meta.seo_title}`,
      description: featureDetail.meta.search_description,
      images: [],
    },
    twitter: {
      card: 'summary',
      site: '@gradehunt',
      creator: '@gradehunt',
      title: `${featureDetail.meta.seo_title}`,
      description: featureDetail.meta.search_description,
      images: [],
    },
  };
}

// Blog page component
export default async function FeaturePage({
  params,
}: {
  params: { slug: string };
}) {
  // Fetch all blogs (with caching for ISR)
  const response = await fetch(
    `${baseUrl}/z/blogs/pages/?type=blogs.FeaturePage&limit=1000`,
    {
      next: { revalidate: 60 }, // Revalidate this data every 60 seconds
    }
  );
  const featurePages = await response.json();
  // Find the blog matching the slug
  const featurePage = featurePages.items?.find(
    (item: any) => item.meta.slug === params.slug
  );

  if (!featurePage) {
    return (
      <div>
        <NotFoundPage />
      </div>
    );
  }

  // Fetch blog details
  const detailResponse = await fetch(featurePage.meta.detail_url, {
    next: { revalidate: 60 },
  });

  const featurePageDetail = await detailResponse.json();
  // console.log('feat', featurePageDetail);
  const courseGroup = featurePageDetail.course_group;
  // const course = courseGroup.toLowerCase();
  const course = courseGroup === 'BOARD' ? 'boards' : 'professional';
  const category = featurePageDetail.category;
  const subcategory = featurePageDetail.subcategory;
  // function replaceUnderscoreWithSpace(inputString: string) {
  //   return inputString.replace(/_/g, '%20');
  // }

  const courseTypeMap: Record<string, number[]> = {
    CA_Foundation: [1],
    CA_Intermediate: [2],
    CA_Final: [4],
    CS_Professional: [5],
    CS_Executive: [6],
    CMA_Final: [7],
    CMA_Inter: [8],
    ICSE_10th: [9],
    ICSE_12th: [10],
    CBSE_10th: [11],
    CBSE_12th: [12],
    Maharashtra_Board_10th: [13],
    Maharashtra_Board_12th: [14],
  };

  return (
    <div className="">
      <WhatsAppIcon />
      {/* <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(schemaData),
        }}
      /> */}

      <article className="blog-parent">
        <div className="hidden md:block">
          <FeaturesPromotionSection
            promotionBanners={featurePageDetail.desktop_banners}
          />
        </div>
        <div className="md:hidden">
          <FeaturesPromotionSection
            promotionBanners={featurePageDetail.mobile_banners}
          />
        </div>
        <div className="md:mt-10">
          {featurePageDetail.course_section && (
            <CoursePageClient
              courseName={featurePageDetail.category.toLowerCase()}
            />
          )}
        </div>
        {featurePageDetail.test_series_type && (
          <section className="px-[20px] pb-8 lg:py-8 lg:px-[100px]">
            <TestSeriesSection
              course={course}
              course_type={courseTypeMap[subcategory] || []}
            />
          </section>
        )}
        {featurePageDetail.features_section && <FeaturesSection />}
        {featurePageDetail.toppers_section && (
          <HomePageSwiper course={course} course_name={category} />
        )}
        {featurePageDetail.faculties_section && <Faculties course={course} />}

        {/* FAQs */}
        <section className="px-[20px] py-8 lg:py-16 lg:px-[100px] lg:pt-20">
          <h2 className="block font-primary_medium text-2xl md:text-3xl lg:text-4xl my-4">
            Frequently Asked Questions
          </h2>
          <div className="w-full bg-gray-50 p-6 rounded-lg shadow-md">
            {featurePageDetail.faqs.map((faq: any, index: number) => (
              <div
                key={index}
                className="mb-8 border-b border-gray-300 pb-4 last:border-none last:pb-0"
              >
                <h3 className="flex items-start text-xl font-semibold text-gray-800 mb-2">
                  <span className="mr-2 text-gray-600">Q{index + 1}.</span>
                  <span className="flex-1">
                    {parseWithBR(faq.value.question.html)}
                  </span>
                </h3>
                <div className="flex items-start max-w-none text-gray-700 leading-relaxed">
                  <span className="mr-2 font-medium text-gray-500">Ans.</span>
                  <span className="flex-1">
                    {parseWithBR(faq.value.answer.html)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Banner Section */}
        {/* {featurePageDetail.banner_image && (
          <section className="relative mb-12">
            <img
              src={featurePageDetail.banner_image.meta.download_url}
              alt={featurePageDetail.title}
              className="w-full h-auto rounded-lg shadow-lg"
            />
            <h1 className="text-4xl md:text-5xl text-left mt-8 font-bold">{featurePageDetail.title}</h1>
          </section>
        )} */}

        <div className="w-full flex flex-col text-justify p-12 bg-gray-50 gap-12">
          <h1 className="text-4xl font-extrabold text-center text-gray-900">
            {parseWithBR(featurePageDetail.title)}
          </h1>

          <section>
            <p className="text-lg text-gray-700 leading-relaxed">
              {parseWithBR(featurePageDetail.description)}
            </p>
          </section>

          {/* Subsections */}
          <section>
            {featurePageDetail.subsections.map(
              (section: any, index: number) => (
                <FeatureSubSection key={index} section={section} />
              )
            )}
          </section>
        </div>
      </article>
    </div>
  );
}
