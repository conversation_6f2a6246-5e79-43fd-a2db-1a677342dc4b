import { baseUrl } from "@/config/constants";
import axios from 'axios';

export const createUserOrder = async (orderOptions: any, token: string | null) => {
    try {
        const response = await axios.post(
            `${baseUrl}/orders/`,
            orderOptions,
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `${token}`
                }
            }
        );

        return response;
    } catch (error) {
        console.error('Error creating user Order API:', error);
        throw error;
    }
}

export const UpdateUserOrder = async (orderOptions: any, currentOrderId: number, token: string | null) => {
    try {
        const response = await axios.patch(
            `${baseUrl}/orders/${currentOrderId}/`,
            orderOptions,
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `${token}`
                }
            }
        );

        return response;
    } catch (error) {
        console.error('Error updating user order API:', error);
        throw error;
    }
};

export const createUserTransaction = async(transactionOptions : any,token : string | null) => {

    try {
        const response = await fetch(`${baseUrl}/transactions/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization' : `${token}`,
            },
            body: JSON.stringify(
                transactionOptions
            ),
        });

        if (!response.ok) {
            throw new Error('Failed to create transactions');
        }

        return await response;
    } catch (error) {
        console.error('Error creating user transactions API :', error);
        throw error;
    }
}