import CoursePageClient from '@/components/navbar/course/CoursePage';
import { Metadata } from 'next';
import DownloadBanner from '@/components/landing/download/download_banner';
import Faculties from '@/components/landing/faculties/Faculties';
import FeaturesSection from '@/components/landing/features/FeaturesSection';
import HeroImageSection from '@/components/Promotion';
import TestSeriesSection from '@/components/landing/landing_test_series/TestSeries';
import HomePageSwiper from '@/components/landing/swiper/home_page_swiper';
import FilterModal from '@/components/test_series/filter_modal';
import WhatsAppIcon from '@/components/dashboard/whatsApp';
import FaqComponent from '@/components/landing/faq/courseFaq';
import CATestSeriesContent from '@/components/course/CaContent';
import BlogsCards from '@/components/blogs/blogsCard';

export async function generateMetadata(): Promise<Metadata> {
  let title = '';
  let description = '';
  let keywords = '';
  let url = '';
  let imageUrl = '';

  if (process.env.NEXT_PUBLIC_IS_PROD == 'true') {
    title = 'CA Test Series | CA Final, CA Inter, CA Foundation | Gradehunt';
    description =
      "Join India's trusted CA test series for Final, Inter, and Foundation. Benefit from expert evaluation, doubt solving, and feedback to help you ace your exams with Gradehunt.";
    keywords =
      'CA Test Series, CA Final Test Series, CA Inter Test Series, CA Mock Test Series, CA Exam Test Series';
    url = 'https://gradehunt.com/course/ca-test-series';
    imageUrl = 'https://gradehunt.com/icons/gradehunt_logo.svg';
  }

  return {
    title,
    description,
    keywords: keywords.split(', '),
    alternates: { canonical: url },
    openGraph: {
      title,
      description,
      url,
      images: [imageUrl],
      type: 'website',
      locale: 'en_US',
    },
    twitter: {
      card: 'summary',
      title,
      description,
      images: [imageUrl],
      site: '@gradehunt',
      creator: '@gradehunt',
    },
    other: {
      rating: 'General',
      Category: 'Education',
      Language: 'en-US',
      distribution: 'global',
    },
  };
}

const CoursePage = () => {
  return (
    <div>
      <WhatsAppIcon />
      <FilterModal />
      <HeroImageSection course="professional" />
      <CoursePageClient courseName="ca" />
      <section className="px-[20px] py-8 lg:px-[100px]">
        <TestSeriesSection course_type={[1, 2, 4]} />
      </section>
      <FeaturesSection />
      <HomePageSwiper course="professional" course_name="CA" />
      <Faculties course="professional" />
      <BlogsCards category="" course="PROFESSIONAL" />
      <DownloadBanner />
      <FaqComponent courseName="ca" />
      <CATestSeriesContent />
    </div>
  );
};

export default CoursePage;
