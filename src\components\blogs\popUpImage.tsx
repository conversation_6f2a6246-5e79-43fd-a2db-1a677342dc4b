'use client';

import {
  openModalWithProfessional,
  openModalWithBoards,
  openModal,
} from '@/redux/features/filterModalSlice';
import React from 'react';
import { useDispatch } from 'react-redux';

interface PopupImageSectionProps {
  popupImageUrl: string;
  altText: string;
  course_group: string;
  category: string;
}

const PopupImageSection: React.FC<PopupImageSectionProps> = ({
  popupImageUrl,
  altText,
  course_group,
  category,
}) => {
  const dispatch = useDispatch();

  const handleClick = () => {
    if (course_group === 'BOARD') {
      console.log(category);
      dispatch(openModalWithBoards(category));
    } else if (course_group === 'PROFESSIONAL') {
      dispatch(openModalWithProfessional(category));
    } else {
      dispatch(openModal());
    }
  };

  return (
    <section onClick={handleClick} className="relative cursor-pointer">
      <img
        src={popupImageUrl}
        alt={altText}
        className="w-full h-auto rounded-lg shadow-lg"
      />
    </section>
  );
};

export default PopupImageSection;
