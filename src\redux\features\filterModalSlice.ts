import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface FilterModalState {
  isOpen: boolean;
  courseGroup: string | null;
  category: string | null;
}

const initialState: FilterModalState = {
  isOpen: false,
  courseGroup: null,
  category: null,
};

const filterModalSlice = createSlice({
  name: 'filterModal',
  initialState,
  reducers: {
    openModal: (state) => {
      state.isOpen = true;
      state.courseGroup = null;
      state.category = null;
    },
    openModalWithBoards: (state, action: PayloadAction<string | undefined>) => {
      state.isOpen = true;
      state.courseGroup = 'boards';
      state.category = action.payload || null;
    },
    openModalWithProfessional: (
      state,
      action: PayloadAction<string | undefined>
    ) => {
      state.isOpen = true;
      state.courseGroup = 'professional';
      state.category = action.payload || null;
    },
    closeModal: (state) => {
      state.isOpen = false;
      state.courseGroup = null;
      state.category = null;
      // Don't reset courseGroup and category to allow re-opening with the same values
    },
  },
});

export const {
  openModal,
  closeModal,
  openModalWithBoards,
  openModalWithProfessional,
} = filterModalSlice.actions;

export default filterModalSlice.reducer;
