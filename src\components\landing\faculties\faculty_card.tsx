import Image from "next/image";

const FacultyCard = ({ imgSrc, facultyName }: { imgSrc: string; facultyName: string; desc: string; }) => {
  return (
    <div className="w-full">
      <Image
        src={imgSrc}
        alt="faculty"
        width={300}
        height={300}
        loading="lazy"
        className="object-cover w-full bg-no-repeat mb-2 border-[0.5px] border-[#B0B0B0] rounded-lg"
      />
      <h2 className="text-[#1D1D1D] text-base lg:text-xl lg:mb-2">
        {facultyName}
      </h2>
      {/* <p className="text-[#3F3F3F] text-[12px] lg:text-base">
        {desc}
      </p> */}
    </div>
  );
}

export default FacultyCard;
