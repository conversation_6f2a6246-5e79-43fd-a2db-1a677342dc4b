'use client';

import CourseColumn from '@/components/landing/courses/course_column';
import { MobileCourseTabs } from '@/components/landing/courses/mobile_course_tabs';
import SubjectPaperSection from '@/components/subject_papers/subject_paper_section';
import { useGetCoursesQuery } from '@/redux/apiSlice';
import { selectUser } from '@/redux/features/userSlice';
import { CourseData } from '@/types/Course';
import { Key } from 'react';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/navigation';
import TestSeriesSectionClient from '@/components/landing/landing_test_series/TestSeriesSectionClient';
const SubjectPaperPage = () => {
  const user = useSelector(selectUser);
  const { data: coursesData } = useGetCoursesQuery(undefined, {
    skip: !user.firebaseUID,
  });
  const router = useRouter();
  return (
    <section className="lg:px-[100px] px-[20px] pt-8 pb-10 lg:pt-16 lg:pb-28 ">
      <div className="flex w-3/5 gap-4 items-center">
        <img
          loading="lazy"
          className="cursor-pointer"
          onClick={() => router.push('/dashboard')}
          src="/icons/back.svg"
          alt="back"
        />
        <h2 className="font-primary_medium text-[#1D1D1D] text-[22px] my-4 leading-7">
          Your Tests
        </h2>
      </div>
      <SubjectPaperSection />
      <div className="lg:grid grid-cols-3 gap-x-8 mt-20 hidden">
        {coursesData?.results.map(
          (course: CourseData, index: Key | null | undefined) => (
            <CourseColumn
              additionalClassNamesForCard=""
              course={course}
              key={index}
            />
          )
        )}
      </div>

      <div className="mt-16">
        <MobileCourseTabs coursesData={coursesData?.results} />
      </div>

      <div className="relative mt-8">
        <TestSeriesSectionClient />
      </div>
    </section>
  );
};

export default SubjectPaperPage;
