import { useEffect, useRef, useState } from 'react';

// Create a separate PayoutModal component
interface PayoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentBalance: number;
  initialVpa: string;
  onSubmit: (vpa: string) => void;
}

const PayoutModal: React.FC<PayoutModalProps> = ({
  isOpen,
  onClose,
  currentBalance,
  initialVpa,
  onSubmit,
}) => {
  const [upiId, setUpiId] = useState(initialVpa);
  const [error, setError] = useState<string | null>(null);
  const modalRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    // Update UPI ID when initialVpa changes
    setUpiId(initialVpa);
  }, [initialVpa]);

  // Handle outside clicks
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  const handleUpiChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUpiId(e.target.value);
    setError(null); // Clear any previous errors
  };

  const validateUpi = (upi: string): boolean => {
    // Basic UPI validation: must contain @ and have text before and after @
    const upiRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9]+$/;
    return upiRegex.test(upi);
  };

  const handleSubmit = () => {
    if (!upiId.trim()) {
      setError('Please enter a UPI ID');
      return;
    }

    if (!validateUpi(upiId)) {
      setError('Please enter a valid UPI ID (e.g., username@provider)');
      return;
    }

    onSubmit(upiId);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div
        ref={modalRef}
        className="bg-white rounded-md w-full max-w-md overflow-hidden relative"
        onClick={(e) => e.stopPropagation()}
      >
        <button
          onClick={onClose}
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-700"
          aria-label="Close dialog"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>

        <div className="p-4 border-b">
          <h2 className="text-xl font-medium">Share payment details</h2>
        </div>

        <div className="p-4">
          <p className="mb-2 text-gray-700">Your UPI ID for payout</p>
          <input
            type="text"
            value={upiId}
            onChange={handleUpiChange}
            placeholder="username@provider"
            className="w-full p-3 border border-gray-300 rounded-md"
          />
          {error && <p className="mt-2 text-red-500 text-sm">{error}</p>}
          {upiId && !error && (
            <p className="mt-3 text-gray-700">
              ₹{currentBalance} will be sent to {upiId}
            </p>
          )}
        </div>

        <div className="p-4 flex justify-end">
          <button
            onClick={handleSubmit}
            className="px-6 py-3 bg-black text-white rounded-md hover:bg-gray-800 transition-colors"
          >
            Submit
          </button>
        </div>
      </div>
    </div>
  );
};

export default PayoutModal;
