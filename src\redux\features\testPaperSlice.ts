import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { TestPaperType, TestStatusData } from "@/types/Test";

interface TestPaperSlice {
  selectedTestPaper: TestPaperType | null;
}

const initialState: TestPaperSlice = {
  selectedTestPaper: null,
};

const testPaperSlice = createSlice({
  name: 'testpaper',
  initialState,
  reducers: {
    setSelectedTestPaper: (state, action: PayloadAction<TestPaperType | null>) => {
      state.selectedTestPaper = action.payload;
    },
    setSelectedTestPaperStatusData: (state, action: PayloadAction<TestStatusData | null>) => {
      if (state.selectedTestPaper) {
        state.selectedTestPaper.test_paper = action.payload;
      }
    },
    setSelectedTestPaperStatus: (state, action: PayloadAction<{ id: number; status: string }>) => {
      if (state.selectedTestPaper) {
        const testIndex = state.selectedTestPaper.subject.tests.findIndex(test => test.id === action.payload.id);
        if (testIndex !== -1) {
          state.selectedTestPaper.subject.tests[testIndex].check_status = action.payload.status;
        }
      }
    },
    updateTestPaperStatus(state, action: PayloadAction<{ id: number; status: string }>) {
      if (state.selectedTestPaper?.test_paper?.id == action.payload.id) {
        state.selectedTestPaper.test_paper.status = action.payload.status;
      }
    },
  },
});

export const { setSelectedTestPaper, setSelectedTestPaperStatusData, updateTestPaperStatus, setSelectedTestPaperStatus } = testPaperSlice.actions;
export default testPaperSlice.reducer;
