import { baseUrl } from '@/config/constants';
import LandingTestSeries from './landing_test_series';

interface CoursePageProps {
  course_type?: number[];
  course?: string;
}

async function fetchTestSeries(course: string, course_type?: number) {
  const url =
    course_type !== undefined
      ? `${baseUrl}/testseries-type/?course_type__course_group__name=${course}&course_type=${course_type}`
      : `${baseUrl}/testseries-type/?course_type__course_group__name=${course}`;

  // console.log('url', url);

  const res = await fetch(url, {
    method: 'GET',
    cache: 'no-store',
  });

  if (!res.ok) {
    throw new Error(
      `Failed to fetch test series${
        course_type !== undefined ? ` for course_type: ${course_type}` : ''
      }`
    );
  }

  const data = await res.json();
  // console.log(
  //   `Fetched ${
  //     course_type !== undefined ? `for course_type ${course_type}` : 'default'
  //   }:`,
  //   data
  // );
  return data;
}

const TestSeriesSection = async ({
  course_type = [],
  course = '',
}: CoursePageProps) => {
  let testSeries: any[] = [];
  // console.log('input data', course, course_type);

  try {
    if (course_type.length === 0) {
      // No specific course_type passed, fetch without that param
      const data = await fetchTestSeries(course);
      testSeries = data;
    } else {
      const promises = course_type.map((type) => fetchTestSeries(course, type));
      const results = await Promise.all(promises);
      testSeries = results.flat();
    }
  } catch (error) {
    console.error('Error fetching test series:', error);
  }

  return (
    <div className="overflow-x-hidden relative">
      <LandingTestSeries allTestSeries={testSeries} />
    </div>
  );
};

export default TestSeriesSection;
