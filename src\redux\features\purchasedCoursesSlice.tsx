// src/redux/purchasedCourseSlice.ts
import { PurchasedTests } from '@/types/Test'; // Assuming this is your item type
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface PurchasedCoursesState {
    purchasedCourses: PurchasedTests; // Correctly defined as an array of PurchasedTestItem
}

const initialState: PurchasedCoursesState = {
    purchasedCourses: [], // Initialize as an empty array
};

const purchasedCoursesSlice = createSlice({
    name: 'purchasedCourses',
    initialState,
    reducers: {
        setPurchasedCourses: (state, action: PayloadAction<PurchasedTests>) => {
            state.purchasedCourses = action.payload; // Correctly assign the payload
        },
        resetPurchasedCourses: (state) => {
            state.purchasedCourses = []; // Correctly assign the payload
        },
    },
});

export const { setPurchasedCourses, resetPurchasedCourses } = purchasedCoursesSlice.actions;

export default purchasedCoursesSlice.reducer;