'use client';

import { useEffect, useState } from 'react';
import CheckoutModal from '@/components/subject/checkout_modal';
import PerCostSubject from '@/components/subject/per_cost_subject';
import FilterModal from '@/components/test_series/filter_modal';
import useRazorpay from 'react-razorpay';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, persistor } from '@/redux/store.ts';
import { razorpay_api_key } from '@/config/constants';
import {
  createRazorpayOrder,
  verifyRazorpayTransaction,
} from '@/api/actions/razorpayActions';
import {
  // UpdateUserOrder,
  createUserOrder,
  // createUserTransaction,
} from '@/api/actions/userOrderTransactionsActions';
import { useRouter } from 'next/navigation';
import { useCreateUserAuthMutation } from '@/redux/apiSlice';
import {
  logoutUser,
  updateIdToken,
  updateUser,
} from '@/redux/features/userSlice';
import { toast } from 'react-hot-toast';
import { ClipLoader } from 'react-spinners';
import { auth } from '@/data/firebase';
// import { PurchasedSubject, PurchasedTests } from "@/types/Test";
// import { Subject } from "@/types/Course";
// import { ClipLoader } from "react-spinners";

const Subject = () => {
  const isOpen = useSelector((state: RootState) => state.filterModal.isOpen);
  const selectedTestSeries = useSelector(
    (state: RootState) => state.testSeries.selectedTestSeries
  );
  const currentUser = useSelector((state: RootState) => state.user);
  // const purchasedCourses: PurchasedTests = useSelector((state: RootState) => state.purchasedCourses.purchasedCourses);
  const dispatch = useDispatch();
  const router = useRouter();
  const [isCheckoutModalOpen, setIsCheckoutModalOpen] = useState(false);
  const [phoneNum, setPhoneNum] = useState(currentUser.phoneNumber ?? '');
  const [totalPrice, setTotalPrice] = useState(0);
  const [selectedSubjects, setSelectedSubjects] = useState<number[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isVerifyingPhone, setIsVerifyingPhone] = useState(false);
  const [, setAccessToken] = useState<string | null>(
    currentUser.authTokens.accessToken ?? null
  );
  const [checkoutPhoneError, setCheckoutPhoneError] = useState<string>('');
  const [, setRazorpayPaymentId] = useState<string | undefined>();
  const [, setRazorpayOrderId] = useState<string | undefined>();
  const [, setRazorpaySignature] = useState<string | undefined>();
  const [discountedFinalPrice, setDiscountedFinalPrice] = useState(0);
  const [Razorpay] = useRazorpay();
  const [createUserAuth] = useCreateUserAuthMutation();
  const parentCourseNameFromState = useSelector(
    (state: RootState) => state.course.selectedParentCourseName
  );
  console.log(parentCourseNameFromState);

  const [couponCode, setCouponCode] = useState<number>();

  const handleCouponSelect = (code: number) => {
    setCouponCode(code);
  };

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged(async (user) => {
      if (user) {
        try {
          const idToken = await user.getIdToken(true);
          dispatch(updateIdToken(idToken));
        } catch (error) {
          console.error('Error fetching ID token:', error);
        }
      } else {
        console.log('No current user');
      }
    });

    // Cleanup the listener on unmount
    return () => unsubscribe();
  }, []);

  // useEffect(() => {
  //   if (!currentUser.tokenResponse.idToken || currentUser.tokenResponse.idToken.length == 0) {
  //     router.push("/");
  //   } else {
  //     setAccessToken(currentUser.tokenResponse.idToken);
  //   }
  // }, [currentUser.userId, navigate]);

  const getFreshToken = async () => {
    const user = auth.currentUser;
    if (!user) {
      console.error('No user is currently logged in.');
      return null;
    }

    try {
      const freshToken = await user.getIdToken(true); // Force refresh token
      dispatch(updateIdToken(freshToken)); // Update Redux store with new token if needed
      return freshToken;
    } catch (error) {
      console.error('Error refreshing ID token:', error);
      return null;
    }
  };

  useEffect(() => {
    if (discountedFinalPrice !== null) {
      setDiscountedFinalPrice((prev) => Math.round(prev * 100) / 100);
    }
  }, [discountedFinalPrice]);

  const handleCreateRazorpayOrder = async (
    tempAccessToken: string,
    currentUserId: any,
    order_id: number
  ) => {
    try {
      setIsLoading(true);
      const freshToken = await getFreshToken(); // Always fetch a fresh token
      if (selectedSubjects.length && selectedTestSeries?.mrp_price) {
        const roundedDiscountedPrice = Math.round(discountedFinalPrice * 100);
        const orderRes = await createRazorpayOrder(
          {
            amount: roundedDiscountedPrice,
            currency: 'INR',
            user_id: currentUserId,
            order_id: order_id,
          },
          freshToken || tempAccessToken
        );

        if (orderRes.ok) {
          const orderData = await orderRes.json();
          return orderData;
        } else {
          throw new Error('Failed to create order');
        }
      } else {
        toast('No subjects selected');
      }
    } catch (error) {
      console.error('Error creating order:', error);
      toast.error('Failed to create order. Please try again.');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyTransaction = async (
    orderId: string,
    paymentId: string,
    signature: string,
    tempAccessToken: string
  ) => {
    try {
      setIsLoading(true);
      const verifyRes = await verifyRazorpayTransaction({
        razorpay_order_id: orderId,
        razorpay_payment_id: paymentId,
        razorpay_signature: signature,
        headerToken: tempAccessToken,
      });

      if (verifyRes.ok) {
        toast.success('Transaction Completed!');
        router.push(
          `/thankyou?course=${encodeURIComponent(
            parentCourseNameFromState ?? ''
          )}`
        );
        return verifyRes;
      } else {
        throw new Error('Failed to verify transaction');
      }
    } catch (error) {
      console.error('Error verifying transaction:', error);
      toast.error('Failed to verify transaction. Please try again.');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const handleRazorpayPayment = async () => {
    if (phoneNum.length !== 10) {
      setCheckoutPhoneError('Phone number must be valid 10 digits');
      setIsVerifyingPhone(false);
      return;
    }
    const [tempAccessToken, currentUserId] = await handleGetAccessToken();
    setIsCheckoutModalOpen(false);
    try {
      setIsLoading(true);
      if (selectedTestSeries?.mrp_price && selectedSubjects.length) {
        const userOrderData = await handleUserOrder(
          discountedFinalPrice,
          tempAccessToken,
          currentUserId
        );
        const orderData = await handleCreateRazorpayOrder(
          tempAccessToken,
          currentUserId,
          userOrderData.id
        );
        if (orderData) {
          const options = {
            key: razorpay_api_key || '',
            amount: orderData.data.amount,
            prefill: { contact: phoneNum },
            readonly: { contact: true },
            currency: 'INR',
            name: 'Gradehunt',
            description: 'UAT Transaction',
            image: '/icons/gradehunt_logo.svg',
            order_id: orderData.data.id,
            handler: async (response: any) => {
              setRazorpayPaymentId(response.razorpay_payment_id);
              setRazorpayOrderId(response.razorpay_order_id);
              setRazorpaySignature(response.razorpay_signature);
              await handleVerifyTransaction(
                response.razorpay_order_id,
                response.razorpay_payment_id,
                response.razorpay_signature,
                tempAccessToken
              );
              // await handleUserOrderAndTransaction(
              //   response.razorpay_order_id,
              //   response.razorpay_payment_id,
              //   response.razorpay_signature,
              //   orderData.data.amount,
              //   tempAccessToken,
              //   currentUserId,
              //   userOrderData
              // );
            },
            theme: {
              color: '#3399cc',
            },
          };

          const rzp1 = new Razorpay(options);

          rzp1.on('payment.failed', (response: any) => {
            console.error(response);
            toast.error('Payment failed. Please try again.');
          });

          rzp1.open();
        }
      } else {
        toast.error('No subjects selected');
      }
    } catch (error) {
      console.error('Error handling Razorpay payment:', error);
    } finally {
      setIsLoading(false);
      setIsVerifyingPhone(false);
    }
  };

  const handleUserOrder = async (
    orderValue: number,
    tempAccessToken: string,
    currentUserId: any
  ) => {
    try {
      setIsLoading(true);
      const freshToken = await getFreshToken(); // Always fetch a fresh token
      const userOrderOptions = {
        user: currentUserId,
        order_value: String(orderValue),
        order_status: 'Pending',
        purchases: [
          {
            test_series: selectedTestSeries?.id,
            subjects: selectedSubjects,
          },
        ],
        coupon_code: couponCode || null,
      };
      const userOrderRes = await createUserOrder(
        userOrderOptions,
        freshToken || tempAccessToken
      );
      return userOrderRes.data;
    } catch (error) {
      console.error('Error finalizing order and transaction:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // const handleUserOrderAndTransaction = async (
  //   razorpayPaymentId: string,
  //   razorpayOrderId: string,
  //   razorpaySignature: string,
  //   orderValue: number,
  //   tempAccessToken: string,
  //   currentUserId: any,
  //   currOrder: any
  // ) => {
  //   try {
  //     setIsLoading(true);

  //     const userOrderOptions = {
  //       order_status: 'Completed',
  //       // user: currOrder.user.id,
  //       // order_value: currOrder.order_value,
  //       // purchases: currOrder.purchases.map((purchase: any) => ({
  //       //   test_series: purchase.test_series.id,
  //       //   subjects: purchase.subjects.map((subject: any) => subject.id),
  //       // })),
  //     };

  //     const userOrderRes = await UpdateUserOrder(
  //       userOrderOptions,
  //       currOrder.id,
  //       tempAccessToken
  //     );

  //     if (userOrderRes.status !== 200) {
  //       toast.error('Error updating order. Please try again later.');
  //       return;
  //     }

  //     const transactionOptions = {
  //       payment_id: razorpayPaymentId,
  //       razorpay_order_id: razorpayOrderId,
  //       signature: razorpaySignature,
  //       payment_status: 'Success',
  //       order_value: String(orderValue),
  //       order_id: userOrderRes.data.id,
  //       user_id: currentUserId,
  //     };

  //     const transactionRes = await createUserTransaction(
  //       transactionOptions,
  //       tempAccessToken
  //     );
  //     if (transactionRes.ok) {
  //       const transactionData = await transactionRes.json();
  //       if (transactionData) {
  //         toast.success('Transaction Completed!');
  //         router.push('/thankyou');
  //         localStorage.setItem('isUserLoggedIn', 'true');
  //       }
  //     }
  //   } catch (error) {
  //     console.error('Error finalizing order and transaction:', error);
  //     toast.error('An unexpected error occurred. Please try again later.'); // Show a general error toast
  //   } finally {
  //     setIsLoading(false);
  //   }
  // };

  const handleGetAccessToken = async () => {
    if (currentUser.tokenResponse.idToken)
      return [currentUser.tokenResponse.idToken, currentUser.userId];
    const bodyData = {
      phone: phoneNum,
    };
    const { data: accessRes } = await createUserAuth({ body: bodyData });

    if (accessRes) {
      setAccessToken(accessRes.access);
      dispatch(
        updateUser({
          userId: accessRes.user_id,
          authTokens: {
            accessToken: accessRes.access,
            refreshToken: accessRes.refresh,
          },
          phoneNumber: phoneNum,
        })
      );
    }
    return [accessRes.access, accessRes.user_id];
  };

  const onOpen = () => {
    setCheckoutPhoneError('');
    if (!selectedSubjects || selectedSubjects.length == 0) {
      toast.error('No subjects selected');
      return;
    }
    if (phoneNum) {
      if (phoneNum.startsWith('+91')) setPhoneNum(phoneNum.slice(3));
      handleRazorpayPayment();
    } else setIsCheckoutModalOpen(true);
  };

  const handleClose = () => {
    setIsCheckoutModalOpen(false);
    if (phoneNum.length !== 10) router.back();
  };

  useEffect(() => {
    if (
      (!currentUser.tokenResponse.idToken &&
        !(currentUser.tokenResponse.idToken.length > 0)) ||
      !phoneNum
    )
      setIsCheckoutModalOpen(true);
  }, [currentUser.tokenResponse.idToken]);

  function handleBack() {
    if (
      currentUser.authTokens.accessToken &&
      !currentUser.tokenResponse.idToken
    ) {
      dispatch(logoutUser());
      persistor.purge();
    }
    router.back();
  }

  return (
    <div>
      {isLoading && (
        <div className="absolute inset-0 bg-white/50 backdrop-blur-sm z-10 flex items-center justify-center">
          <ClipLoader color="#3498db" size={50} />
        </div>
      )}
      <section className="lg:px-[100px] px-[20px] py-10">
        <div className="lg:hidden flex items-start mb-4 mx-auto w-full gap-4">
          <div className="flex gap-4">
            <img
              loading="lazy"
              className="cursor-pointer"
              onClick={handleBack}
              src="/icons/back.svg"
              alt="back"
            />
            {/* <h2 className="font-primary_medium lg:block hidden text-base text-[#1D1D1D]">
              Test Series
            </h2> */}
          </div>
          <div className="flex flex-row w-full justify-between">
            {selectedTestSeries?.title}
          </div>
        </div>
        <FilterModal isOpen={isOpen} />
        <CheckoutModal
          isOpen={isCheckoutModalOpen}
          onClose={handleClose}
          phoneNum={phoneNum}
          setPhoneNum={setPhoneNum}
          checkoutPhoneError={checkoutPhoneError}
          isVerifyingPhone={isVerifyingPhone}
          setIsVerifyingPhone={setIsVerifyingPhone}
          selectedTestSeries={selectedTestSeries?.id ?? 0}
        />
        <div className="hidden md:flex justify-between items-center mb-4 pb-4 mx-auto max-w-7xl border-b-[0.5px] border-b-[#3F3F3F]">
          <div className="flex gap-4 items-center">
            <img
              loading="lazy"
              className="cursor-pointer"
              onClick={handleBack}
              src="/icons/back.svg"
              alt="back"
            />
            <h2 className="font-primary_medium lg:block hidden text-base text-[#1D1D1D]">
              {selectedTestSeries?.title}
            </h2>
          </div>
          {/* <div className="flex flex-row items-center gap-3">
              <span>{selectedTestSeries?.course_type.title}</span>|
              <span>{selectedTestSeries?.test_series_type.title}</span>|
              <span>2024</span>
              <div className="text-[#2F50FF] cursor-pointer ml-8" onClick={handleOpenModal}>
                <IoFilterSharp size={24} />
              </div>
            </div> */}
        </div>
        <PerCostSubject
          totalPrice={totalPrice}
          setTotalPrice={setTotalPrice}
          selectedSubjects={selectedSubjects}
          setSelectedSubjects={setSelectedSubjects}
          selectedTestSeries={selectedTestSeries}
          onOpen={onOpen}
          setDiscountedFinalPrice={setDiscountedFinalPrice}
          isLoading={isLoading}
          onCouponSelect={handleCouponSelect}
          phoneNum={phoneNum}
        />
      </section>
    </div>
  );
};

export default Subject;
