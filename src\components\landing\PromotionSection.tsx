'use client';

import { useState, useRef, useEffect } from 'react';

type Banner = {
  id: number;
  title: string;
  web_image_url: string;
  mobile_image_url: string;
  order: number;
};

interface PromotionData {
  count: number;
  next: string | null;
  previous: string | null;
  results: Banner[];
}

interface PromotionSectionProps {
  promotionBanners: PromotionData;
}

const PromotionSection: React.FC<PromotionSectionProps> = ({
  promotionBanners,
}) => {
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const startAutoSlide = () => {
    intervalRef.current = setInterval(() => {
      setCurrentBannerIndex((prevIndex) =>
        prevIndex === promotionBanners?.results?.length - 1 ? 0 : prevIndex + 1
      );
    }, 2500); // 2.5 seconds per slide
  };

  const handleDotClick = (index: number) => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    setCurrentBannerIndex(index);
    startAutoSlide(); // Reinitialize the interval after a dot is clicked
  };

  useEffect(() => {
    startAutoSlide();
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [promotionBanners.results]);

  if (!promotionBanners?.results?.length) {
    return (
      <div className="w-full h-[370px] md:h-[435px] bg-gray-200 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-shimmer"></div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full overflow-x-hidden">
      <div className="w-full h-full">
        <div
          className="hidden md:flex w-full h-full transition-transform duration-1000 ease-in-out"
          style={{ transform: `translateX(-${currentBannerIndex * 100}%)` }}
        >
          {promotionBanners?.results?.map((banner, index) => (
            <div key={index} className="w-full h-full flex-shrink-0">
              <img
                width={2560}
                height={860}
                loading={currentBannerIndex === index ? 'eager' : 'lazy'}
                src={banner.web_image_url}
                alt={banner.title}
                className="w-full h-full object-cover"
              />
            </div>
          ))}
        </div>
        <div
          className="w-full h-full flex md:hidden transition-transform duration-1000 ease-in-out"
          style={{ transform: `translateX(-${currentBannerIndex * 100}%)` }}
        >
          {promotionBanners?.results?.map((banner, index) => (
            <div key={index} className="w-full h-full flex-shrink-0">
              <img
                width={800}
                height={800}
                loading={currentBannerIndex === index ? 'eager' : 'lazy'}
                src={banner.mobile_image_url}
                alt={banner.title}
                className="w-full h-full object-cover"
              />
            </div>
          ))}
        </div>
        <div className="absolute left-0 right-0 bottom-4 flex justify-center space-x-2">
          {promotionBanners?.results?.map((_, index) => (
            <button
              key={index}
              onClick={() => handleDotClick(index)}
              className={`w-3 h-3 rounded-full ${
                index === currentBannerIndex ? 'bg-dot' : 'bg-gray-400'
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default PromotionSection;
