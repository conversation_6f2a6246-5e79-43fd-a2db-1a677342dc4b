'use client';

import { useRouter } from 'next/navigation';
import { FaArrowUpLong } from 'react-icons/fa6';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import TestSeriesSectionClient from '@/components/landing/landing_test_series/TestSeriesSectionClient';
import FilterModal from '@/components/test_series/filter_modal';

const SamplePapers = () => {
  const courseLinks = [
    {
      course: 'Maharashtra',
      courseTypes: [
        {
          title: 'Maharashtra Board 10th and 12th',
          link: 'https://drive.google.com/drive/u/1/folders/1RaSPe_nLsWP6OxF_Lc28nT7aS2yHQjLh',
        },
      ],
    },
    {
      course: 'CBSE',
      courseTypes: [
        {
          title: 'CBSE 10th and 12th',
          link: 'https://drive.google.com/drive/u/1/folders/19rMM9uR-MuxscqjE9S1AeYsBRVaabOMB',
        },
      ],
    },
    {
      course: 'ICSE',
      courseTypes: [
        {
          title: 'ICSE 10th and 12th',
          link: 'https://drive.google.com/drive/u/1/folders/1Pc9WWxqV5i4-7vRvFjqUjgyqdy8iuPIi',
        },
      ],
    },
  ];

  const router = useRouter();
  return (
    <section className="lg:px-[100px] px-[20px] py-10 space-y-8 lg:space-y-16">
      <FilterModal />
      <div className="space-y-8">
        <div className="flex gap-4 items-center">
          <img
            loading="lazy"
            className="cursor-pointer"
            onClick={() => router.back()}
            src="/icons/back.svg"
            alt="back"
          />
          {/* <AboutUsTabs currentTab={currentTab} setCurrentTab={setCurrentTab} /> */}
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-primary_medium font-medium text-[#1D1D1D]">
            Sample Papers
          </h2>
        </div>
      </div>
      <div>
        <h2 className="hidden lg:block font-primary_medium text-2xl md:text-3xl lg:text-4xl my-4">
          Courses
        </h2>
        <div className={`hidden lg:grid lg:grid-cols-3 gap-8`}>
          {courseLinks?.map((course) => (
            <div className="lg:w-auto w-full">
              <h2 className="text-[#1D1D1D] max-w-[90%] border-b-[1.5px] border-[#E3E3E3] font-normal leading-[46px] text-[40px]  ">
                {course.course}
              </h2>

              <div className="lg:flex lg:flex-col grid grid-cols-2 mt-4 gap-6 lg:gap-6 justify-start">
                {course.courseTypes.map((courseData, index) => (
                  <div key={index}>
                    <a href={courseData.link} target="_blank">
                      <div
                        className={`bg-[#EEEEEE] w-[40vw] h-[40vw] md:h-full hover:bg-[#CFE0FF] cursor-pointer transition-all duration-150 md:w-full md:h-full lg:w-full xl:w-full flex lg:flex-row flex-col-reverse justify-end items-center lg:justify-between p-4 rounded-lg shadow-md group`}
                      >
                        <div className="lg:mr-0 lg:pt-0 pt-4 mr-auto lg:mt-0">
                          <h2 className="text-[#1D1D1D] font-primary font-medium text-[16px] leading-7 mb-0">
                            {courseData.title}
                          </h2>
                        </div>

                        <div className="bg-[#FFFFFF] text-[#FFAE1E] group-hover:bg-[#2F50FF] group-hover:text-[white] cursor-pointer flex justify-center items-center rounded-full p-3 lg:ml-0 ml-auto">
                          <FaArrowUpLong
                            size={14}
                            className="rotate-45 w-full h-full ease-in-out transition-all duration-200"
                          />
                        </div>
                      </div>
                    </a>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="my-8">
        <Tabs defaultValue={'CA'} className="w-full lg:hidden block">
          <h2 className="ml-2 font-primary_medium text-2xl">Courses</h2>
          <TabsList className="bg-transparent grid grid-cols-3 place-content-center my-2 gap-2">
            {courseLinks.map((course, index) => (
              <TabsTrigger
                key={index}
                className="px-10 py-2 border-[#FFAE1E] border-[0.5px]"
                value={course.course}
              >
                {course.course}
              </TabsTrigger>
            ))}
          </TabsList>
          <hr className="my-2 text-[#E3E3E3] w-[inherit]" />

          {courseLinks.map((name, index) => (
            <TabsContent key={index} value={name.course}>
              <div className="lg:hidden flex w-full flex-wrap">
                {courseLinks
                  ?.filter((course) => course.course === name.course)
                  .map((course, index) => (
                    <div className="grid grid-cols-2 gap-4">
                      {course.courseTypes.map((courseData) => (
                        <div key={index}>
                          <a href={courseData.link} target="_blank">
                            <div
                              className={`bg-[#EEEEEE] w-[40vw] h-[40vw] md:h-full hover:bg-[#CFE0FF] cursor-pointer transition-all duration-150 md:w-full md:h-full lg:w-full xl:w-full flex lg:flex-row flex-col-reverse justify-end items-center lg:justify-between p-4 rounded-lg shadow-md group`}
                            >
                              <div className="lg:mr-0 lg:pt-0 pt-4 mr-auto lg:mt-0">
                                <h2 className="text-[#1D1D1D] font-primary font-medium text-[16px] leading-7 mb-0">
                                  {courseData.title}
                                </h2>
                              </div>

                              <div className="bg-[#FFFFFF] text-[#FFAE1E] group-hover:bg-[#2F50FF] group-hover:text-[white] cursor-pointer flex justify-center items-center rounded-full p-3 lg:ml-0 ml-auto">
                                <FaArrowUpLong
                                  size={14}
                                  className="rotate-45 w-full h-full ease-in-out transition-all duration-200"
                                />
                              </div>
                            </div>
                          </a>
                        </div>
                      ))}
                    </div>
                  ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
      <TestSeriesSectionClient course="boards" />
    </section>
  );
};

export default SamplePapers;
