import { CourseType, Subject } from "./Course";

export interface TestData {
  title: string;
  description: string;
}

export interface TestSeriesType {
  id: number;
  title: string;
  description: string;
  test_series_type: {
    id: number;
    title: string;
    description: string;
    created_at: string;
    updated_at: string;
  };
  course_type: CourseType;
  status: boolean;
  discounted_price: number;
  mrp_price: number;
  fees_structure: string;
  syllabus: string;
  created_at: string;
  updated_at: string;
  valid_from : string,
  valid_till : string,
}

export interface Test {
  id: number; // Unique identifier for the test
  test_title: string; // Title of the test
  answer_key: string; // URL to the answer key
  question_paper: string; // URL to the question paper
  time_duration: number; // Duration of the test in minutes
  total_marks: number; // Total marks for the test
  valid_from: string; // Start date of the test validity
  valid_to: string; // End date of the test validity
  check_status: string; // Availability status of the test
}

export interface PurchasedSubject {
  id: number; // Unique identifier for the financial report
  title: string; // Title of the financial report
  description: string; // Description of the financial report
  tests: Test[]; // Array of tests associated with the financial report
}

export type PurchasedTestItem = {
  test_series: TestSeriesType;
  subjects: PurchasedSubject[]
};

export type TestPaperType = {
  test_series: TestSeriesType;
  subject: {
    "id": number,
    "title": string,
    "description": string,
    "course_type": number,
    "tests": 
        {
            "id": number,
            "test_title": string,
            "test_series": TestSeriesType
            "valid_from": string,
            "valid_to": string,
            "subject":Subject,
            "time_duration": number,
            "total_marks": number,
            "question_paper": string,
            "answer_key": string,
            "created_at": string
            check_status : string,
        }[]
  }
  test_paper: TestStatusData | null;
};

export type PurchasedTests = PurchasedTestItem[];

export type Notes = {
  notes_url : string,
  notes_type : string,
  subject : {
    id : number,
    course_type : CourseType,
    title : string,
    description : string,
  }
}

export type NotesResponse = {
  "MCQ" : Notes[],
  "DAILY" : Notes[],
  "REVISION" : Notes[],
  "SUMMARY" : Notes[]
}

export type TestStatusData = {
  id: number;
  test_title: string;
  test_series: TestSeriesType;
  subject: number;
  valid_from: string;
  valid_to: string;
  time_duration: number;
  total_marks: number;
  question_paper: string | null;
  answer_key: string | null;
  status: string;
};

export interface TestSubmissionItem {
  id: number;
  test: {
    id: number;
    test_title: string;
    test_series: TestSeriesType;
    subject: Subject;
    time_duration: number;
    total_marks: number;
    created_at: string;
  };
  user: {
    id: number;
    phone: string;
    email: string;
    name: string;
    gender: string;
  };
  assigned_teacher: string | null;
  remarks: string;
  submitted_answer_sheet: string;
  checked_answer_sheet: string | null;
  check_status: boolean;
  created_at: string;
}

export type TestSubmissionData = TestSubmissionItem[];
