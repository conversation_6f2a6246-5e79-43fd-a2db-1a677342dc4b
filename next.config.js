// next.config.js
import { withSentryConfig } from "@sentry/nextjs";

/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ["gradehunt-object-storage.blr1.cdn.digitaloceanspaces.com", "gradehunt-object-storage.blr1.digitaloceanspaces.com"],
  },
};

// Wrap Next.js config with Sentry
const sentryConfig = withSentryConfig(nextConfig, {
  org: "alemeno-kw",
  project: "gradehunt-frontend",
  silent: !process.env.CI,
  widenClientFileUpload: true,
  reactComponentAnnotation: { enabled: true },
  tunnelRoute: "/monitoring",
  hideSourceMaps: true,
  disableLogger: true,
  automaticVercelMonitors: true,
  environment: process.env.NEXT_PUBLIC_SENTRY_ENV
});

// Use `export default` instead of `module.exports`
export default sentryConfig;
