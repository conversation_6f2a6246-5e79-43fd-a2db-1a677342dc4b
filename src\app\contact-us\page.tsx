'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast, Toaster } from 'react-hot-toast';
import { Input } from '@/components/ui/input';

const ContactUsPage = () => {
  const router = useRouter();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    message: '',
    smsConsent: false,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]:
        type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Basic validation
    if (
      !formData.name.trim() ||
      !formData.email.trim() ||
      !formData.message.trim()
    ) {
      toast.error('Please fill in all required fields');
      setIsSubmitting(false);
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast.error('Please enter a valid email address');
      setIsSubmitting(false);
      return;
    }

    // Simulate form submission
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast.success('Message submitted successfully!');

      // Reset form
      setFormData({
        name: '',
        email: '',
        phone: '',
        message: '',
        smsConsent: false,
      });
    } catch (error) {
      toast.error('Failed to submit message. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Toaster position="top-right" />
      <section className="lg:px-[100px] px-[20px] py-6 space-y-6">
        <div className="space-y-4">
          <div className="flex gap-4 items-center">
            <img
              loading="lazy"
              className="cursor-pointer"
              onClick={() => router.back()}
              src="/icons/back.svg"
              alt="back"
            />
            <h2 className="text-xl md:text-2xl lg:text-3xl font-primary_medium font-medium text-[#1D1D1D]">
              Contact Us
            </h2>
          </div>
        </div>

        <div className="max-w-xl mx-auto">
          <div className="bg-white rounded-2xl shadow-lg p-6 lg:p-8">
            <h1 className="text-2xl lg:text-3xl font-bold text-center text-[#2C3E50] mb-6">
              Contact Us
            </h1>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Input
                  type="text"
                  name="name"
                  placeholder="Your name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full h-12 px-4 text-base border-2 border-gray-300 rounded-lg bg-gray-50 placeholder:text-gray-400 placeholder:tracking-wide focus:border-[#2F50FF] focus:bg-white transition-all duration-200"
                  required
                />
              </div>

              <div>
                <Input
                  type="email"
                  name="email"
                  placeholder="Your email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full h-12 px-4 text-base border-2 border-gray-300 rounded-lg bg-gray-50 placeholder:text-gray-400 placeholder:tracking-wide focus:border-[#2F50FF] focus:bg-white transition-all duration-200"
                  required
                />
              </div>

              <div>
                <Input
                  type="tel"
                  name="phone"
                  placeholder="Phone Number"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="w-full h-12 px-4 text-base border-2 border-gray-300 rounded-lg bg-gray-50 placeholder:text-gray-400 focus:border-[#2F50FF] focus:bg-white transition-all duration-200"
                />
              </div>

              <div>
                <textarea
                  name="message"
                  placeholder="Your message"
                  value={formData.message}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-4 py-3 text-base border-2 border-gray-300 rounded-lg bg-gray-50 placeholder:text-gray-400 placeholder:tracking-wide focus:border-[#2F50FF] focus:bg-white transition-all duration-200 resize-none"
                  required
                />
              </div>

              <div className="flex items-start gap-3">
                <input
                  type="checkbox"
                  id="smsConsent"
                  name="smsConsent"
                  required={true}
                  checked={formData.smsConsent}
                  onChange={handleInputChange}
                  className="mt-1 w-4 h-4 text-[#2F50FF] border-2 border-gray-300 rounded focus:ring-[#2F50FF] focus:ring-2"
                />
                <label
                  htmlFor="smsConsent"
                  className="text-gray-700 text-sm leading-relaxed"
                >
                  I hereby authorize you to send notifications via SMS/RCS
                  Messages/Promotional/Informational Messages.
                </label>
              </div>

              <div className="pt-3">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-[#B91C1C] hover:bg-[#991B1B] text-white font-semibold text-lg py-3 px-6 rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? 'Submitting...' : 'Submit'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </section>
    </>
  );
};

export default ContactUsPage;
