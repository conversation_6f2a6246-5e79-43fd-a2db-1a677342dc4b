import { FaChevronRight } from "react-icons/fa"

const DashboardTestCard = ({ testTitle, testDesc, validTill, validFrom }: { testTitle: string, testDesc: string, validTill: string, validFrom: string }) => {
    return (
        <div className='bg-[#EEEEEE] w-full rounded-lg p-4 flex justify-between items-center lg:h-auto'>
            <div className="w-4/5 md:w-2/5">
                <p className="text-[#000000] text-base leading-5 font-normal">
                    {testTitle
                        .toLowerCase()
                        .replace(/\b\w/g, (char) => char.toUpperCase())
                    }
                </p>
                <p className="text-[#49454F] text-[12px] lg:text-sm leading-5 font-normal">{testDesc}</p>
                <p className="text-[#49454F] mt-2 lg:hidden block text-[12px] leading-4 font-normal">
                    Valid From : {validFrom}
                </p>
                <p className="text-[#49454F] mt-2 lg:hidden block text-[12px] leading-4 font-normal">
                    Valid Till : {validTill}
                </p>
            </div>
            <div className="lg:block hidden">
                <p className="text-[#49454F] font-primary_medium text-base leading-5 font-normal">Valid From : {validFrom}</p>
            </div>
            <div className="lg:block hidden">
                <p className="text-[#49454F] font-primary_medium text-base leading-5 font-normal">Valid Till {validTill}</p>
            </div>

            <div className="bg-[#FCFCFC] text-[#FFAE1E] hover:bg-[#2F50FF] transition-all duration-200 ease-in-out hover:text-[white] cursor-pointer flex justify-center items-center rounded-full p-3 ">
                <FaChevronRight size={12} />
            </div>
        </div>
    )
}

export default DashboardTestCard