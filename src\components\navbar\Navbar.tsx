'use client';

import { useEffect, useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import PrimaryButton from '@/components/ui/PrimaryButton';
import MenuButton from '@/components/ui/menu_button';
import { RootState, persistor } from '@/redux/store.ts';
import { useDispatch, useSelector } from 'react-redux';
import { logoutUser, updateUser } from '@/redux/features/userSlice';
import { useGetCoursesQuery } from '@/redux/apiSlice';
import { selectParentCourseName } from '@/redux/features/selectedFilterModalCourse';
import { setSelectedCoursePage } from '@/redux/features/coursePageSlice';
import { resetPurchasedCourses } from '@/redux/features/purchasedCoursesSlice';

const Navbar = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const pathname = usePathname();

  const courseRoutes = [
    '/course/icse-test-series',
    '/course/cbse-test-series',
    '/course/maharashtra-board-test-series',
  ];

  const queryArg =
    pathname.startsWith('/boards') || courseRoutes.includes(pathname)
      ? 'boards'
      : 'professional';

  const { data: coursesData } = useGetCoursesQuery(queryArg, {
    refetchOnMountOrArgChange: true,
  });
  const currentUser = useSelector((state: RootState) => state.user);
  const [isOpen, setIsOpen] = useState(false);
  const courseNames = [
    ...new Set(coursesData?.results.map((course) => course.name)),
  ];
  const [isCoursesDropdownOpen, setIsCoursesDropdownOpen] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    router.prefetch('/auth/login');
  }, []);

  useEffect(() => {
    if (
      pathname == '/' &&
      (currentUser.authTokens.accessToken || currentUser.tokenResponse.idToken)
    )
      router.push('/dashboard');
  }, [pathname]);

  useEffect(() => {
    setIsCoursesDropdownOpen(false);
  }, []);

  const handleAuthClick = async () => {
    if (
      currentUser.authTokens.accessToken &&
      (!currentUser.firebaseUID || currentUser.firebaseUID.length === 0)
    ) {
      dispatch(
        updateUser({
          authTokens: {
            accessToken: '',
            refreshToken: '',
          },
        })
      );
      router.push('/');
    } else if (
      !currentUser.firebaseUID ||
      currentUser.firebaseUID.length === 0
    ) {
      router.push('/auth/login');
    } else {
      localStorage.setItem('isUserLoggedIn', 'false');
      dispatch(logoutUser());
      dispatch(resetPurchasedCourses());
      persistor.purge();
      router.push('/');
    }
    setIsOpen(false);
  };

  const handleNavCourseClick = (name: string) => {
    dispatch(selectParentCourseName(name));
    const course = coursesData?.results.find((course) => course.name == name);
    const arrayCoursePage = course ? [course] : null;
    dispatch(setSelectedCoursePage(arrayCoursePage));
    const formattedName = course?.name?.toLowerCase().replace(/\s+/g, '-');
    router.push(`/course/${formattedName}-test-series`);
    setIsCoursesDropdownOpen(false);
  };

  const handleNavMobileCourseClick = (name: string) => {
    dispatch(selectParentCourseName(name));
    const course = coursesData?.results.find((course) => course.name == name);
    const arrayCoursePage = course ? [course] : null;
    dispatch(setSelectedCoursePage(arrayCoursePage));
    dispatch(
      course
        ? setSelectedCoursePage(arrayCoursePage)
        : setSelectedCoursePage(null)
    );
    const formattedName = course?.name?.toLowerCase().replace(/\s+/g, '-');
    router.push(`/course/${formattedName}-test-series`);
    setIsOpen(false);
    // dispatch(openModal());
    // setIsOpen(false); // Close the menu after clicking
  };

  const scrollToFAQs = () => {
    const element = document.getElementById('faqs');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const toggleDropdown = () => {
    setIsCoursesDropdownOpen((prev) => !prev);
  };

  // const [isUserLoggedIn, setIsUserLoggedIn] = useState<Boolean>(false)
  // useEffect(()=>{
  //   setIsUserLoggedIn((localStorage.getItem('isUserLoggedIn') || "") === "true")
  // },[])

  const isAuthenticated =
    // (currentUser.tokenResponse.idToken &&
    //   currentUser.tokenResponse.idToken.length != 0) ||
    currentUser.authTokens.accessToken &&
    currentUser.authTokens.accessToken.length != 0;

  return (
    <nav className="px-[20px] font-primary lg:px-[100px] h-[68px] bg-[#1D1D1D] flex justify-between items-center relative">
      {/* Logo Section */}
      <div onClick={() => router.push('/')} className="p-2">
        <img
          loading="lazy"
          src="/icons/gradehunt_logo.svg"
          className="h-[64px] cursor-pointer"
          alt="gradehunt_logo"
        />
      </div>

      {/* Desktop View */}
      <div className="justify-center items-center md:flex hidden">
        <ul className="text-[#FCFCFC] flex gap-12 leading-[20px] tracking-[0.15px] text-[16px] font-medium">
          <li className="cursor-pointer" onClick={() => router.push('/blogs')}>
            Blogs
          </li>
          <li
            className="cursor-pointer"
            onClick={() =>
              pathname === '/boards'
                ? router.push('/boards/about-us')
                : pathname === '/professional'
                ? router.push('/professional/about-us')
                : router.push('/about-us')
            }
          >
            About
          </li>
          <li className="relative">
            <span
              className="cursor-pointer flex items-center"
              onClick={toggleDropdown}
            >
              Courses
              <svg
                className={`ml-1 h-4 w-4 transition-transform ${
                  isCoursesDropdownOpen ? 'rotate-180' : ''
                }`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </span>
            {isCoursesDropdownOpen && (
              <div className="absolute z-[555] top-full left-0 mt-2">
                <CoursesDropdown
                  courses={courseNames}
                  onCourseClick={handleNavCourseClick}
                  isVisible={isCoursesDropdownOpen}
                />
              </div>
            )}
          </li>
          <li
            className="cursor-pointer"
            onClick={() => {
              scrollToFAQs();
              setIsOpen(false);
            }}
          >
            FAQs
          </li>
        </ul>

        {/* Authentication Button */}
        <div onClick={handleAuthClick}>
          <PrimaryButton
            additionalClassNames="ml-[100px]"
            title={
              isMounted ? (isAuthenticated ? 'Logout' : 'Login') : 'Loading...'
            }
          />
        </div>
      </div>

      {/* Mobile View */}
      <div className="ml-auto md:hidden flex gap-4">
        <div onClick={handleAuthClick}>
          <PrimaryButton
            additionalClassNames="text-sm"
            title={
              isMounted ? (isAuthenticated ? 'Logout' : 'Login') : 'Loading...'
            }
          />
        </div>
        <MenuButton isOpen={isOpen} setIsOpen={setIsOpen} />
      </div>

      {/* Mobile Dropdown */}
      {isMounted && (
        <div
          className={`transition-[max-height] z-50 duration-300 ease-in-out overflow-hidden ${
            isOpen ? 'block' : 'hidden max-h-0'
          } w-full left-0 border-t-[0.5px] px-[20px] py-5 bg-[#1D1D1D] absolute top-[68px]`}
        >
          <div className="flex flex-row justify-between border-b-[1px] border-[#FFFFFF] pb-4">
            <img
              loading="lazy"
              src="/icons/profile.svg"
              className="w-[36px] h-[36px]"
              alt="profile"
            />
          </div>
          <ul className="text-[#FCFCFC] text-base flex flex-col justify-start gap-2 py-4">
            <li onClick={() => router.push('/blogs')}>Blogs</li>
            <li onClick={() => router.push('/about-us')}>About</li>
            <li>
              <p className="border-b-[#FCFCFC] border-b-[0.5px]">Courses</p>
              <div className="flex flex-row flex-wrap gap-2 pb-4 py-2 w-full snap-x">
                {courseNames.map((name) => (
                  <div
                    key={name}
                    onClick={() => handleNavMobileCourseClick(name)}
                    className="flex-shrink-0 snap-start"
                  >
                    <DropdownCourseButton title={name} />
                  </div>
                ))}
              </div>
            </li>
            <li
              onClick={() => {
                scrollToFAQs();
                setIsOpen(false);
              }}
            >
              FAQs
            </li>
          </ul>
        </div>
      )}
    </nav>
  );
};

export default Navbar;

const CoursesDropdown = ({
  courses,
  onCourseClick,
  isVisible,
}: {
  courses: any;
  onCourseClick: any;
  isVisible: boolean;
}) => {
  return (
    <div>
      <div
        className={`bg-[#1D1D1D] border border-[#FCFCFC] rounded-lg shadow-lg w-[300px] overflow-hidden ${
          isVisible ? '' : 'hidden'
        }`}
      >
        <div className="p-4 grid grid-cols-2 gap-4">
          {courses.length === 0 ? (
            <p className="text-[#FCFCFC] text-sm">No courses available</p>
          ) : (
            courses.map((course: any) => (
              <div
                key={course}
                className="bg-[#2A2A2A] rounded-lg overflow-hidden hover:bg-[#dea440] transition-colors duration-300 cursor-pointer"
                onClick={() => onCourseClick(course)}
              >
                <div className="p-4">
                  <h3 className="text-[#FCFCFC] font-semibold mb-2">
                    {course}
                  </h3>
                  <p className="text-[#FCFCFC] text-sm">
                    Explore {course} courses
                  </p>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

function DropdownCourseButton({ title }: { title: string }) {
  return (
    <div className="bg-[#FFF3DF] rounded-[37px] px-8 py-0.5 font-primary_medium text-base text-[#1D1D1D] ">
      {title}
    </div>
  );
}
