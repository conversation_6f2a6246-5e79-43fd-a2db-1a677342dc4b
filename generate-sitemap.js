import { SitemapStream, streamToPromise } from 'sitemap';
import fs from 'fs';

// Define the routes based on your router configuration
const routes = [
  { path: '/', priority: 1.0, changefreq: 'daily' },
  { path: '/auth/login', priority: 0.8, changefreq: 'monthly' },
  { path: '/test-series/filter', priority: 0.8, changefreq: 'monthly' },
  { path: '/subject/:subjectId', priority: 0.7, changefreq: 'monthly' },
  { path: '/dashboard', priority: 0.8, changefreq: 'weekly' },
  { path: '/purchased/tests/', priority: 0.7, changefreq: 'monthly' },
  { path: '/purchased/subject/paper/:paperid', priority: 0.7, changefreq: 'monthly' },
  { path: '/thankyou', priority: 0.7, changefreq: 'monthly' },
  { path: '/about-us', priority: 0.7, changefreq: 'monthly' },
  { path: '/how-it-works', priority: 0.7, changefreq: 'monthly' },
  { path: '/our-features', priority: 0.7, changefreq: 'monthly' },
  { path: '/sample-papers', priority: 0.7, changefreq: 'monthly' },
  { path: '/course/ca-test-series', priority: 0.7, changefreq: 'monthly' },
  { path: '/course/cs-test-series', priority: 0.7, changefreq: 'monthly' },
  { path: '/course/cma-test-series', priority: 0.7, changefreq: 'monthly' },
  { path: '*', priority: 0.0, changefreq: 'never' },
];

// Create a sitemap stream
const sitemapStream = new SitemapStream({ hostname: 'https://gradehunt.com' }); // Replace with your actual domain

// Add each route to the sitemap stream
routes.forEach(route => {
  sitemapStream.write({ url: route.path, changefreq: route.changefreq, priority: route.priority });
});

// End the sitemap stream
sitemapStream.end();

// Generate and write the sitemap to a file
streamToPromise(sitemapStream).then(data => {
  fs.writeFileSync('./public/sitemap.xml', data.toString());
  console.log('Sitemap generated: public/sitemap.xml');
}).catch(err => {
  console.error('Error generating sitemap:', err);
});