import CoursePageClient from '@/components/navbar/course/CoursePage';
import { Metadata } from 'next';
import DownloadBanner from '@/components/landing/download/download_banner';
import Faculties from '@/components/landing/faculties/Faculties';
import FeaturesSection from '@/components/landing/features/FeaturesSection';
import HeroImageSection from '@/components/Promotion';
import TestSeriesSection from '@/components/landing/landing_test_series/TestSeries';
import HomePageSwiper from '@/components/landing/swiper/home_page_swiper';
import FilterModal from '@/components/test_series/filter_modal';
import WhatsAppIcon from '@/components/dashboard/whatsApp';
import FaqComponent from '@/components/landing/faq/courseFaq';
import CMATestSeriesContent from '@/components/course/CmaContent';
import BlogsCards from '@/components/blogs/blogsCard';

export async function generateMetadata(): Promise<Metadata> {
  let title = '';
  let description = '';
  let keywords = '';
  let url = '';
  let imageUrl = '';

  if (process.env.NEXT_PUBLIC_IS_PROD == 'true') {
    title = 'CMA Test Series | CMA Final, Inter, Foundation | Gradehunt';
    description =
      "Join Gradehunt's Online Test Series and Mentorship for CMA Final, Inter, and Foundation. Get evaluations in 1-2 days and personalized guidance from industry experts!";
    keywords =
      'CMA Test Series, CMA Inter Test Series, CMA Final Test Series, CMA Foundation Test Series';
    url = 'https://gradehunt.com/course/cma-test-series';
    imageUrl = 'https://gradehunt.com/icons/gradehunt_logo.svg';
  }

  return {
    title,
    description,
    keywords: keywords.split(', '),
    alternates: { canonical: url },
    openGraph: {
      title,
      description,
      url,
      images: [imageUrl],
      type: 'website',
      locale: 'en_US',
    },
    twitter: {
      card: 'summary',
      title,
      description,
      images: [imageUrl],
      site: '@gradehunt',
      creator: '@gradehunt',
    },
    other: {
      rating: 'General',
      Category: 'Education',
      Language: 'en-US',
      distribution: 'global',
    },
  };
}

const CoursePage = () => {
  return (
    <div>
      <WhatsAppIcon />
      <FilterModal />
      <HeroImageSection course="professional" />
      <CoursePageClient courseName="cma" />
      <section className="px-[20px] py-8 lg:py-8 lg:px-[100px]">
        <TestSeriesSection course_type={[7, 8]} />
      </section>
      <FeaturesSection />
      <HomePageSwiper course="professional" course_name="CMA" />
      <Faculties course="professional" />
      <BlogsCards category="" course="PROFESSIONAL" />
      <DownloadBanner />
      <FaqComponent courseName="cma" />
      <CMATestSeriesContent />
    </div>
  );
};

export default CoursePage;
