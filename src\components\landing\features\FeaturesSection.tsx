'use client';

import { usePathname } from 'next/navigation';
import HowItWorksCard from './how_it_works_card';

const FeaturesSection = () => {
  const pathname = usePathname();
  const getLink = (
    boardPath: string,
    professionalPath: string,
    defaultPath: string
  ) => {
    const courseRoutes = [
      '/course/icse-test-series',
      '/course/cbse-test-series',
      '/course/maharashtra-board-test-series',
    ];

    const professionalCourseRoutes = [
      '/course/cma-test-series',
      '/course/cs-test-series',
      '/course/ca-test-series',
    ];

    if (courseRoutes.includes(pathname)) {
      return `/boards/${boardPath}`;
    }

    if (professionalCourseRoutes.includes(pathname)) {
      return `/professional/${professionalPath}`;
    }

    if (pathname === '/boards') {
      return `/boards/${boardPath}`;
    }

    if (pathname === '/professional') {
      return `/professional/${professionalPath}`;
    }

    return defaultPath;
  };

  return (
    <section className="px-6 py-12 lg:py-20 lg:px-20 flex flex-col gap-12 lg:gap-4 lg:flex-row justify-around items-center bg-gradient-to-r from-blue-50 to-blue-100">
      <HowItWorksCard
        title="How it Works"
        desc="A students needs to follow a simple procedure to download the test papers, write answers on notebook and upload it."
        link={getLink('about-us', 'about-us', '/about-us')}
      />

      <HowItWorksCard
        title="Our Unique Features"
        desc="Gradehunt is the one of the oldest test series provider, serving 1 Lakh+ students with quality tests, evaluation & Doubt solving."
        link={getLink('our-features', 'our-features', '/our-features')}
      />

      <HowItWorksCard
        title="Sample Papers"
        desc="Click here to download Sample Test Papers and Checked Sheets to know how the test papers are drafted and evaluation is done."
        link={getLink('sample-papers', 'sample-papers', '/sample-papers')}
      />
    </section>
  );
};

export default FeaturesSection;
