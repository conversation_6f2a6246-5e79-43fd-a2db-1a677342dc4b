'use client';

import { Key, useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/navigation';
import { AnimatePresence } from 'framer-motion';
import <PERSON><PERSON><PERSON>oader from 'react-spinners/ClipLoader';
import toast, { Toaster } from 'react-hot-toast';
import { CourseData, Subject } from '@/types/Course';
import DashboardTestCard from '@/components/dashboard/dashboard_test_card';
import {
  useGetCoursesQuery,
  useGetNotesQuery,
  useGetPurchasedTestsQuery,
  useGetReferalsHistoryQuery,
  useGetReferalsPayoutRequestQuery,
  useGetReferalsQuery,
  useRequestReferralPayoutMutation,
  useUpdateUserVpaMutation,
} from '@/redux/apiSlice';
import { setSelectedTestPaper } from '@/redux/features/testPaperSlice';
import { toggleSubjectView } from '@/redux/features/subjectViewSlice';
import { RootState, persistor } from '@/redux/store';
import { NotesResponse, PurchasedTestItem } from '@/types/Test';
import { MobileCourseTabs } from '@/components/landing/courses/mobile_course_tabs';
import FilterModal from '@/components/test_series/filter_modal';
import UserProfileStepsModal from './user_profile_steps_modal';
import { Dialog, DialogContent } from '@/components/ui/dialog';
// import SubjectPaperSection from "@/components/subject_papers/subject_paper_section";
import CourseColumn from '@/components/landing/courses/course_column';
import { useUserData } from '@/lib/hooks/useUserData';
import {
  resetPurchasedCourses,
  setPurchasedCourses,
} from '@/redux/features/purchasedCoursesSlice';
import WhatsAppIcon from './whatsApp';
import {
  logoutUser,
  updateIdToken,
  updateUser,
} from '@/redux/features/userSlice';
import TestSeriesSectionClient from '../landing/landing_test_series/TestSeriesSectionClient';
import { auth } from '@/data/firebase';
import DashboardNotesCard from './dashboard_notes_card';
import axios from 'axios';
import { baseUrl } from '@/config/constants';
import PayoutModal from './PayoutModal';
import { FaCopy, FaWhatsapp } from 'react-icons/fa';

// VerificationModal component definition
interface VerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const VerificationModal: React.FC<VerificationModalProps> = ({
  isOpen,
  onClose,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md p-8">
        <div className="flex flex-col items-center p-6">
          <img
            loading="lazy"
            src="/icons/verified.svg"
            alt="Verified"
            className="w-36 h-36 mb-6"
          />
          <h2 className="text-2xl font-primary_medium mb-4">Verified</h2>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Main DashboardClient component definition
const DashboardClient = () => {
  const dispatch = useDispatch();
  const router = useRouter();

  // Redux selectors
  const user = useSelector((state: RootState) => state.user);
  const isOpen = useSelector((state: RootState) => state.filterModal.isOpen);
  // const isSubjectView = useSelector((state: RootState) => state.subjectView.isSubjectView);

  // Component state
  const [isUserProfileModalOpen, setIsUserProfileModalOpen] = useState(false);
  const [processCompleted, setProcessCompleted] = useState(false);
  const [loading, setLoading] = useState(true);
  const [isVerificationModalOpen, setIsVerificationModalOpen] = useState(false);
  const [spinnerColor] = useState('#30b954');
  const [activeTab, setActiveTab] = useState('tests');
  const [activeNotesType, setActiveNotesType] =
    useState<keyof NotesResponse>('MCQ');

  const [showPayoutModal, setShowPayoutModal] = useState(false);

  // Fetch user data and course/test details
  const { isLoading: userLoading } = useUserData();
  const [headerToken, setHeaderToken] = useState('');
  const notesTypes: Array<keyof NotesResponse> = [
    'MCQ',
    'DAILY',
    'REVISION',
    'SUMMARY',
  ];
  console.log(user);
  // Firebase token fetch
  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged(async (user) => {
      if (user) {
        try {
          const idToken = await user.getIdToken(true);
          setHeaderToken(idToken);
          dispatch(updateIdToken(idToken));
        } catch (error) {
          console.error('Error fetching ID token:', error);
        }
      } else {
        console.log('No current user');
      }
    });

    // Cleanup the listener on unmount
    return () => unsubscribe();
  }, []);

  // Mutations
  const [updateVpa] = useUpdateUserVpaMutation();
  const [requestPayout] = useRequestReferralPayoutMutation();

  // Handle VPA submission
  const handleVpaSubmit = async (vpa: string) => {
    if (!headerToken) return;

    try {
      await updateVpa({ headerToken, vpa, id: userReferals.id }).unwrap();
      toast.success('UPI ID updated successfully');
      setShowPayoutModal(false);
    } catch (err) {
      console.error('Error updating UPI ID:', err);
      toast.error('Failed to update UPI ID. Please try again.');
    }
  };

  // Handle payout request
  const handleRequestPayout = async () => {
    // If no VPA exists, open the modal to add one
    if (!userReferals?.vpa) {
      setShowPayoutModal(true);
      return;
    }

    if (userReferals?.balance_amount < 100) {
      toast.error(
        'You need at least ₹100 in your referral balance to proceed.'
      );
      return;
    }

    if (
      userReferalsReq &&
      userReferalsReq.results &&
      userReferalsReq.results.length > 0
    ) {
      // Find the most recent request by sorting by created_at date
      const sortedRequests = [...userReferalsReq.results].sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
      console.log(sortedRequests);
      const latestRequest = sortedRequests[0];

      // Check if the latest request is still being processed
      if (!latestRequest.paid) {
        toast.error(
          'Your previous payout request is still being processed. Please wait until it completes.'
        );
        return;
      }
    }

    if (!headerToken) return;

    try {
      await requestPayout({
        headerToken,
        id: userReferals.id,
        bal_amnt: userReferals.balance_amount,
      }).unwrap();
      toast.success('Payout request submitted successfully');
    } catch (err) {
      console.error('Error requesting payout:', err);
      toast.error('Failed to request payout. Please try again.');
    }
  };

  // Memoize the query parameters to prevent unnecessary re-renders
  const purchasedTestsQueryParams = useMemo(
    () => ({
      headerToken: headerToken || user.authTokens?.accessToken,
      firebaseuid: user?.firebaseUID,
    }),
    [headerToken, user.authTokens?.accessToken, user?.firebaseUID]
  );

  const shouldSkipQueries =
    !headerToken && (!user?.authTokens?.accessToken || !user?.firebaseUID);

  // Data fetching with memoized parameters
  const {
    data: userPurchasedCourses,
    isLoading: purchasesLoading,
    error,
  } = useGetPurchasedTestsQuery(purchasedTestsQueryParams, {
    skip: shouldSkipQueries,
    refetchOnMountOrArgChange: true,
  });

  const {
    data: userNotes,
    // isLoading: notesLoading,
    // error: errors,
  } = useGetNotesQuery(
    {
      headerToken: headerToken || user.authTokens?.accessToken,
    },
    {
      skip: shouldSkipQueries,
      refetchOnMountOrArgChange: true,
    }
  );

  const {
    data: userReferals,
    isLoading: referalsLoading,
    error: referalErrors,
  } = useGetReferalsQuery(
    {
      headerToken: headerToken || user.authTokens?.accessToken,
      userId: user.userId?.toString() || '',
    },
    {
      skip: shouldSkipQueries,
      refetchOnMountOrArgChange: true,
    }
  );

  const referralHistoryParams = useMemo(
    () => ({
      headerToken: headerToken || user.authTokens?.accessToken,
      referrer: userReferals?.id,
    }),
    [headerToken, user.authTokens?.accessToken, userReferals?.id]
  );

  const {
    data: userReferalsHistory,
    isLoading: referalsHistoryLoading,
    error: referalHistoryErrors,
  } = useGetReferalsHistoryQuery(referralHistoryParams, {
    skip: shouldSkipQueries || !userReferals?.id,
    refetchOnMountOrArgChange: true,
  });

  const referralPayoutReqParams = useMemo(
    () => ({
      headerToken: headerToken || user.authTokens?.accessToken,
      referrer: userReferals?.id,
    }),
    [headerToken, user.authTokens?.accessToken, userReferals?.id]
  );

  const {
    data: userReferalsReq,
    isLoading: referalsReqLoading,
    error: referalReqErrors,
  } = useGetReferalsPayoutRequestQuery(referralPayoutReqParams, {
    skip: shouldSkipQueries || !userReferals?.id,
    refetchOnMountOrArgChange: true,
  });

  const { data: coursesData } = useGetCoursesQuery(undefined, {
    skip: !user.firebaseUID,
  });

  // Check authentication and loading status
  useEffect(() => {
    if (!user.tokenResponse?.idToken && !user.authTokens?.accessToken) {
      router.push('/');
      console.log('logged out 1');
    } else if (!userLoading) {
      setLoading(false);
    }
  }, [
    user.tokenResponse.idToken,
    user.authTokens.accessToken,
    userLoading,
    purchasesLoading,
    router,
  ]);

  // Display user profile completion modal if data is incomplete
  // useEffect(() => {
  //   if (user.authTokens.accessToken && !user.tokenResponse.idToken) {
  //     axios
  //       .get(`${baseUrl}/users/${user.userId}`, {
  //         headers: {
  //           Authorization: user?.authTokens?.accessToken,
  //         },
  //       })
  //       .then((res) => {
  //         const userData = res.data;
  //         if (userData) {
  //           if (
  //             !userData.firebase_uid ||
  //             !userData.name ||
  //             !userData.email ||
  //             !userData.phone
  //           )
  //             setIsUserProfileModalOpen(true);
  //           else {
  //             dispatch(
  //               updateUser({
  //                 name: userData.name,
  //                 email: userData.email,
  //                 firebaseUID: userData.firebase_uid,
  //                 phoneNumber: userData.phone,
  //               })
  //             );
  //           }
  //         } else {
  //           dispatch(logoutUser());
  //           dispatch(resetPurchasedCourses());
  //           persistor.purge();
  //           console.log('logged out 2');
  //           router.push('/');
  //         }
  //       })
  //       .catch((res) => {
  //         if (res.status == 403) {
  //           dispatch(logoutUser());
  //           dispatch(resetPurchasedCourses());
  //           persistor.purge();
  //           console.log('logged out 3');
  //           router.push('/');
  //         }
  //       });
  //   } else if (
  //     user &&
  //     (!user.firebaseUID || !user.name || !user.email || !user.phoneNumber)
  //   ) {
  //     setIsUserProfileModalOpen(true);
  //   } else handleCloseUserProfileStepsModal;
  // }, []);

  useEffect(() => {
    if (!user || !user.userId || !user?.tokenResponse?.idToken) {
      dispatch(logoutUser());
      dispatch(resetPurchasedCourses());
      persistor.purge();
      console.log('logged out - invalid auth state');
      router.push('/');
      return;
    }

    axios
      .get(`${baseUrl}/users/${user.userId}`, {
        headers: {
          Authorization: user?.tokenResponse?.idToken || headerToken,
        },
      })
      .then((res) => {
        const userData = res.data;
        if (userData) {
          if (
            !userData.firebase_uid ||
            !userData.name ||
            !userData.email ||
            !userData.phone
          )
            setIsUserProfileModalOpen(true);
          else {
            dispatch(
              updateUser({
                name: userData.name,
                email: userData.email,
                firebaseUID: userData.firebase_uid,
                phoneNumber: userData.phone,
              })
            );
          }
        } else {
          // User exists in system but no data found
          dispatch(logoutUser());
          dispatch(resetPurchasedCourses());
          persistor.purge();
          console.log('logged out - no user data');
          router.push('/');
        }
      })
      .catch((err) => {
        console.error('Error fetching user data:', err);
        // Check if it's an authorization error
        if (err.response && err.response.status === 403) {
          dispatch(logoutUser());
          dispatch(resetPurchasedCourses());
          persistor.purge();
          console.log('logged out - authorization error');
          router.push('/');
        }
      });
  }, []);

  useEffect(() => {
    if (
      user.firebaseUID &&
      user.name &&
      user.email &&
      user.phoneNumber &&
      !user.tokenResponse.idToken
    ) {
      auth.signOut();
      dispatch(logoutUser());
      dispatch(resetPurchasedCourses());
      persistor.purge();
      console.log('logged out 4');
      router.push('/');
    }
  }, [user]);

  // Handle profile steps modal close
  const handleCloseUserProfileStepsModal = () => {
    if (!user.firebaseUID || !user.email || !user.name) {
      toast.error('You have to complete these steps');
      return;
    }
    setProcessCompleted(processCompleted ? processCompleted : false);
    setIsUserProfileModalOpen(false);
  };

  const closeUserProfileModal = () => {
    setIsUserProfileModalOpen(false);
  };

  // useEffect(()=>{
  //   dispatch(toggleSubjectView(false));
  // },[])

  // Handle test paper selection
  const handleSelectTestPaper = useCallback(
    (test: PurchasedTestItem, subject: any) => {
      const testPaper = {
        test_series: test.test_series,
        subject,
        test_paper: null,
      };
      dispatch(setSelectedTestPaper(testPaper));
      router.push('/purchased/tests');
      dispatch(toggleSubjectView(true));
    },
    [dispatch, router]
  );

  useEffect(() => {
    dispatch(setPurchasedCourses(userPurchasedCourses || []));
    router.prefetch('/purchased/tests');
  }, [userPurchasedCourses]);

  useEffect(() => {
    if (
      !purchasesLoading &&
      ((userPurchasedCourses && userPurchasedCourses?.length === 0) ||
        !userPurchasedCourses) &&
      user.authTokens.accessToken &&
      !user.tokenResponse.idToken
    ) {
      dispatch(logoutUser());
      dispatch(resetPurchasedCourses());
      persistor.purge();
      console.log('logged out 5');

      router.push('/');
      auth.signOut();
    }
  }, [purchasesLoading, userPurchasedCourses, user.authTokens, headerToken]);

  // Handle verification modal close
  const handleCloseVerificationModal = () => setIsVerificationModalOpen(false);

  if (error && !user.authTokens.accessToken) {
    router.push('/auth/login');
    localStorage.setItem('isAuthTokenExpired', 'true');
    localStorage.setItem('isUserLoggedIn', 'false');
  }

  // Function to check if a test is currently valid (within validity period)
  const isTestValid = (validFrom: string, validTill: string) => {
    const currentDate = new Date();

    const [fromYear, fromMonth, fromDay] = validFrom.split('-').map(Number);
    const fromDate = new Date(fromYear, fromMonth - 1, fromDay);

    const [tillYear, tillMonth, tillDay] = validTill.split('-').map(Number);
    const tillDate = new Date(tillYear, tillMonth - 1, tillDay);

    return currentDate >= fromDate && currentDate <= tillDate;
  };

  // Filter valid tests from purchased courses
  const validPurchasedCourses = useMemo(() => {
    if (!userPurchasedCourses) return [];

    return userPurchasedCourses.filter((test: any) =>
      isTestValid(test.test_series.valid_from, test.test_series.valid_till)
    );
  }, [userPurchasedCourses]);

  // Main component render
  return loading ||
    referalsLoading ||
    referalsHistoryLoading ||
    referalsReqLoading ? (
    <section className="min-h-screen flex justify-center items-center lg:px-[100px] px-[20px] lg:pt-16 lg:pb-28">
      <ClipLoader color={spinnerColor} loading={loading} size={150} />
    </section>
  ) : (
    <div className="relative">
      <WhatsAppIcon />
      <div className="z-[999]">
        <Toaster />
      </div>
      {/* <HeroImageSection course="professional" /> */}
      {/* {((userPurchasedCourses && userPurchasedCourses?.length === 0) ||
        !userPurchasedCourses) &&
        headerToken && <HeroImageSection course="professional" />} */}
      <section className="lg:px-[100px] px-[20px] pt-8 pb-10 lg:pt-16 lg:pb-28">
        <AnimatePresence mode="wait">
          <div>
            <UserProfileStepsModal
              setIsUserProfileModalOpen={setIsUserProfileModalOpen}
              isOpen={isUserProfileModalOpen}
              closeUserProfileModal={closeUserProfileModal}
              onClose={handleCloseUserProfileStepsModal}
              setIsVerificationModalOpen={setIsVerificationModalOpen}
            />
            <VerificationModal
              isOpen={isVerificationModalOpen}
              onClose={handleCloseVerificationModal}
            />
            <FilterModal isOpen={isOpen} />
            <div className="w-full">
              {/* Tabs Section */}

              <div className="flex gap-8 items-center">
                {/* Tab for Tests */}
                <button
                  className={`font-primary_medium text-[#1D1D1D] text-2xl py-2 ${
                    activeTab === 'tests' ? 'border-b-2 border-[#1D1D1D]' : ''
                  }`}
                  onClick={() => setActiveTab('tests')}
                >
                  Your Tests
                </button>

                {/* Tab for Notes */}
                <button
                  className={`font-primary_medium hidden text-[#1D1D1D] text-2xl py-2 ${
                    activeTab === 'notes' ? 'border-b-2 border-[#1D1D1D]' : ''
                  }`}
                  onClick={() => setActiveTab('notes')}
                >
                  Your Notes
                </button>

                <button
                  className={`font-primary_medium text-[#1D1D1D] text-2xl py-2 ${
                    activeTab === 'referral'
                      ? 'border-b-2 border-[#1D1D1D]'
                      : ''
                  }`}
                  onClick={() => setActiveTab('referral')}
                >
                  Referral Program
                </button>
              </div>

              {/* Content Section */}
              <div className="my-4 ">
                {/* Notes Section */}
                {activeTab === 'notes' && (
                  <div className="">
                    <div className="flex gap-8 items-center">
                      {/* Tab for Notes Type */}
                      {notesTypes.map((type) => (
                        <button
                          className={`font-primary_medium text-[#1D1D1D] md:text-lg py-2 ${
                            activeNotesType === type
                              ? 'border-b-2 border-[#1D1D1D]'
                              : ''
                          }`}
                          onClick={() => setActiveNotesType(type)}
                        >
                          {type}
                        </button>
                      ))}
                    </div>
                    <>
                      {userNotes &&
                        userNotes[activeNotesType]?.length !== 0 && (
                          <div className="flex flex-col gap-2 mt-4">
                            {userNotes[activeNotesType]?.map(
                              (notes: any, index: number) => (
                                <div
                                  key={`${index}`}
                                  className="cursor-pointer grid md:grid-cols-2 gap-4"
                                >
                                  <DashboardNotesCard
                                    subjectTitle={notes.subject.title}
                                    courseTypeTitle={
                                      notes.subject.course_type.title
                                    }
                                    notesURL={notes.notes_url}
                                  />
                                </div>
                              )
                            )}
                          </div>
                        )}
                    </>
                  </div>
                )}

                {/* Tests Section */}
                {activeTab === 'tests' && (
                  <div>
                    <>
                      {validPurchasedCourses?.length !== 0 ? (
                        <div className="flex flex-col gap-2 mt-4">
                          {validPurchasedCourses?.map(
                            (test: any, index: number) =>
                              test.subjects.map(
                                (subject: Subject, subIndex: number) => (
                                  <div
                                    key={`${index}-${subIndex}`}
                                    onClick={() =>
                                      handleSelectTestPaper(test, subject)
                                    }
                                    className="cursor-pointer"
                                  >
                                    <DashboardTestCard
                                      testDesc={test.test_series.title}
                                      testTitle={subject.title}
                                      validFrom={test.test_series.valid_from}
                                      validTill={test.test_series.valid_till}
                                    />
                                  </div>
                                )
                              )
                          )}
                        </div>
                      ) : (
                        <div className="flex flex-col items-center justify-center p-6 mt-4 bg-gray-50 rounded-lg text-center">
                          <div className="text-gray-500 mb-2">
                            {userPurchasedCourses?.length === 0
                              ? 'No tests purchased yet'
                              : 'No tests available'}
                          </div>
                          <p className="text-sm text-gray-300">
                            {userPurchasedCourses?.length === 0
                              ? 'Purchase a test series to see your courses here'
                              : 'All your purchased tests have expired or are not yet available'}
                          </p>
                          {/* <button
                            className="mt-4 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors"
                            onClick={() => router.push('/test-series')}
                          >
                            Browse Test Series
                          </button> */}
                        </div>
                      )}
                    </>
                  </div>
                )}

                {activeTab === 'referral' && (
                  <div className="">
                    {/* Referral Banner */}
                    <section className="p-4 bg-yellow-100 rounded-xl">
                      <p className="text-sm">
                        Share your referral code with friends and family. Every
                        time someone uses your referral code and buys a product,
                        you earn a referral bonus of 20%! 🎉
                      </p>
                    </section>
                    {referalsLoading ? (
                      <div className="flex flex-col items-center justify-center py-12 space-y-4">
                        <div className="animate-spin rounded-full h-10 w-10 border-4 border-gray-300 border-t-black"></div>
                        <p className="text-gray-600">
                          Loading your referral information...
                        </p>
                      </div>
                    ) : referalErrors ? (
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        Unable to load referral data
                      </h3>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-8">
                        <article className="bg-gray-100 rounded-xl p-6 relative my-auto">
                          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                            <div>
                              <h2 className="text-sm text-gray-600 mb-1">
                                Your referral code
                              </h2>
                              <p className="text-3xl font-bold text-gray-800">
                                {userReferals?.referral_code?.code || '-'}
                              </p>
                            </div>
                            <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
                              <button
                                className="px-2 py-3 text-sm bg-black text-white rounded-lg hover:bg-gray-800 transition-colors flex items-center justify-center gap-1"
                                onClick={() =>
                                  navigator.clipboard.writeText(
                                    userReferals?.referral_code?.code || '-'
                                  )
                                }
                              >
                                <FaCopy size={15} /> Copy Code
                              </button>
                              <a
                                href={`https://wa.me/?text=${encodeURIComponent(
                                  `Hey! Check out GradeHunt courses and use my referral code: ${
                                    userReferals?.referral_code?.code || '-'
                                  } to get 20% discount! https://gradehunt.com/`
                                )}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="px-2 py-3 text-sm text-center bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center gap-1"
                              >
                                <FaWhatsapp size={20} /> Share Code
                              </a>
                            </div>
                          </div>
                        </article>

                        {/* Referral Balance Card */}
                        <article className="bg-gray-100 rounded-xl p-6 flex justify-between items-center">
                          <div>
                            <h2 className="text-sm text-gray-800">
                              Your referral balance
                            </h2>
                            <p className="text-3xl font-bold">
                              ₹{userReferals?.balance_amount || '0'}
                            </p>

                            {userReferals?.vpa && (
                              <div className="mt-2 flex items-center text-sm text-gray-600">
                                <span>UPI: {userReferals.vpa}</span>
                                <button
                                  onClick={() => setShowPayoutModal(true)}
                                  className="ml-2 text-blue-600 hover:text-blue-800"
                                >
                                  Edit
                                </button>
                              </div>
                            )}
                          </div>

                          <button
                            className="px-8 py-4 text-xs cursor-pointer bg-black text-white rounded-xl hover:bg-gray-800 transition-colors"
                            onClick={() => handleRequestPayout()}
                          >
                            Request Payout
                          </button>
                        </article>

                        <section>
                          <h2 className="text-sm font-medium my-3 ">
                            Referral history:
                          </h2>
                          <div className="bg-gray-100 rounded-lg overflow-hidden">
                            <table className="w-full">
                              <thead className="bg-gray-200">
                                <tr>
                                  <th className="py-3 px-4 text-left font-medium text-gray-700">
                                    Referral code used by
                                  </th>
                                  <th className="py-3 px-4 text-center font-medium text-gray-700">
                                    Status
                                  </th>
                                  <th className="py-3 px-4 text-right font-medium text-gray-700">
                                    Rupees earned
                                  </th>
                                </tr>
                              </thead>
                              <tbody>
                                {referalsHistoryLoading ? (
                                  <tr>
                                    <td
                                      colSpan={3 as number}
                                      className="py-4 text-center"
                                    >
                                      Loading...
                                    </td>
                                  </tr>
                                ) : referalHistoryErrors ? (
                                  <tr>
                                    <td
                                      colSpan={3 as number}
                                      className="py-4 text-center text-red-500"
                                    >
                                      Error loading referral history
                                    </td>
                                  </tr>
                                ) : userReferalsHistory?.results?.length > 0 ? (
                                  userReferalsHistory.results.map(
                                    (referral: any) => (
                                      <tr
                                        key={referral.id}
                                        className="border-t border-gray-300"
                                      >
                                        <td className="py-3 px-4">
                                          {referral.order.user.phone}
                                        </td>
                                        <td className="py-3 px-4 text-center">
                                          <span
                                            className={
                                              referral.order.order_status ===
                                              'Completed'
                                                ? 'text-green-500'
                                                : 'text-red-500'
                                            }
                                          >
                                            {referral.order.order_status}
                                          </span>
                                        </td>
                                        <td className="py-3 px-4 text-right">
                                          {referral.referral_amount ||
                                          referral.referral_amount === 0
                                            ? `₹${referral.referral_amount}`
                                            : '-'}
                                        </td>
                                      </tr>
                                    )
                                  )
                                ) : (
                                  // Display empty rows with dashes instead of "No referral history found"
                                  [1, 2, 3].map((i) => (
                                    <tr
                                      key={`empty-history-${i}`}
                                      className="border-t border-gray-300"
                                    >
                                      <td className="py-3 px-4">-</td>
                                      <td className="py-3 px-4 text-center">
                                        -
                                      </td>
                                      <td className="py-3 px-4 text-right">
                                        -
                                      </td>
                                    </tr>
                                  ))
                                )}
                              </tbody>
                            </table>
                          </div>
                        </section>

                        <section>
                          <h2 className="text-sm font-medium my-3">
                            Referral payout
                          </h2>
                          <div className="bg-gray-100 rounded-lg overflow-hidden">
                            <table className="w-full">
                              <thead className="bg-gray-200">
                                <tr>
                                  <th className="py-3 px-4 text-left font-medium text-gray-700">
                                    Payout request no.
                                  </th>
                                  <th className="py-3 px-4 text-center font-medium text-gray-700">
                                    Amount
                                  </th>
                                  <th className="py-3 px-4 text-right font-medium text-gray-700">
                                    Status
                                  </th>
                                </tr>
                              </thead>
                              <tbody>
                                {referalsReqLoading ? (
                                  <tr>
                                    <td
                                      colSpan={3 as number}
                                      className="py-4 text-center"
                                    >
                                      Loading...
                                    </td>
                                  </tr>
                                ) : referalReqErrors ? (
                                  <tr>
                                    <td
                                      colSpan={3 as number}
                                      className="py-4 text-center text-red-500"
                                    >
                                      Error loading referral payouts
                                    </td>
                                  </tr>
                                ) : userReferalsReq?.results?.length > 0 ? (
                                  userReferalsReq.results.map(
                                    (referral: any, index: number) => (
                                      <tr
                                        key={`payout-${index}`}
                                        className="border-t border-gray-300"
                                      >
                                        <td className="py-3 px-4">
                                          #{referral?.payout_request_number}
                                        </td>
                                        <td className="py-3 px-4 text-center">
                                          ₹{referral?.amount}
                                        </td>
                                        <td className="py-3 px-4 text-right">
                                          <span
                                            className={
                                              referral.paid
                                                ? 'text-green-500'
                                                : 'text-red-500'
                                            }
                                          >
                                            {referral.paid
                                              ? 'Completed'
                                              : 'Being processed'}
                                          </span>
                                        </td>
                                      </tr>
                                    )
                                  )
                                ) : (
                                  // Display empty rows with dashes instead of "No referral history found"
                                  [1, 2, 3].map((i) => (
                                    <tr
                                      key={`empty-payout-${i}`}
                                      className="border-t border-gray-300"
                                    >
                                      <td className="py-3 px-4">-</td>
                                      <td className="py-3 px-4 text-center">
                                        -
                                      </td>
                                      <td className="py-3 px-4 text-right">
                                        -
                                      </td>
                                    </tr>
                                  ))
                                )}
                              </tbody>
                            </table>
                          </div>
                        </section>
                        {!userReferals?.vpa || showPayoutModal ? (
                          <PayoutModal
                            isOpen={showPayoutModal}
                            onClose={() => setShowPayoutModal(false)}
                            currentBalance={userReferals?.balance_amount || 0}
                            initialVpa={userReferals?.vpa || ''}
                            onSubmit={handleVpaSubmit}
                          />
                        ) : null}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="flex flex-col mt-10">
              <h1 className="text-3xl mb-5">Courses</h1>
              <div className="lg:grid grid-cols-3 gap-x-8 gap-y-5 hidden">
                {coursesData?.results.map(
                  (course: CourseData, index: Key | null | undefined) => (
                    <CourseColumn
                      additionalClassNamesForCard=""
                      course={course}
                      key={index}
                    />
                  )
                )}
              </div>
            </div>

            <div>
              <MobileCourseTabs coursesData={coursesData?.results} />
            </div>
            <div className="relative mt-8">
              <TestSeriesSectionClient />
            </div>
          </div>
        </AnimatePresence>
      </section>
    </div>
  );
};

export default DashboardClient;
