import CoursePageClient from '@/components/navbar/course/CoursePage';
import { Metadata } from 'next';
import DownloadBanner from '@/components/landing/download/download_banner';
import Faculties from '@/components/landing/faculties/Faculties';
import FeaturesSection from '@/components/landing/features/FeaturesSection';
import HeroImageSection from '@/components/Promotion';
import TestSeriesSection from '@/components/landing/landing_test_series/TestSeries';
import HomePageSwiper from '@/components/landing/swiper/home_page_swiper';
import FilterModal from '@/components/test_series/filter_modal';
import WhatsAppIcon from '@/components/dashboard/whatsApp';
import FaqComponent from '@/components/landing/faq/courseFaq';
import BlogsCards from '@/components/blogs/blogsCard';
import CBSETestSeriesContent from '@/components/course/CBSETestSeriesContent';

export async function generateMetadata(): Promise<Metadata> {
  let title = '';
  let description = '';
  let keywords = '';
  let url = '';
  let imageUrl = '';

  if (process.env.NEXT_PUBLIC_IS_PROD == 'true') {
    title = 'CBSE Board Exams Test Series for Class 10 and 12 | Gradehunt';
    description =
      'Start your preparation for CBSE Board exams with our online test series and mentorship program. Learn how to score 90%+ marks in Class 10 and 12 Board exams.';
    keywords =
      'CBSE Test Series, CBSE Class 10 Test Series, CBSE Class 12 Test Series, CBSE Board Preparation, Gradehunt Test Series, CBSE Online Coaching, Class 10 Board Exams, Class 12 Online Test Series, CBSE 2025 Preparation, Score 90% in CBSE Boards';
    url = 'https://gradehunt.com/course/cbse-test-series';
    imageUrl = 'https://gradehunt.com/icons/gradehunt_logo.svg';
  }

  return {
    title,
    description,
    keywords: keywords.split(', '),
    alternates: { canonical: url },
    openGraph: {
      title,
      description:
        "Ace your CBSE Board exams with Gradehunt's expert-led online test series and mentorship program. Score 90%+ in Class 10 & 12",
      url,
      images: [imageUrl],
      type: 'website',
      locale: 'en_US',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description:
        'Start your CBSE Class 10 & 12 Board exam prep with Gradehunt. Online test series, expert mentorship & 90%+ score strategy.',
      images: [imageUrl],
      site: '@gradehunt',
      creator: '@gradehunt',
    },
    other: {
      rating: 'General',
      Category: 'Education',
      Language: 'en-US',
      distribution: 'global',
      author: 'Gradehunt',
      viewport: 'width=device-width, initial-scale=1.0',
      robots: 'index, follow',
    },
  };
}

const CoursePage = () => {
  return (
    <div>
      <WhatsAppIcon />
      <FilterModal />
      <HeroImageSection course="boards" />
      <CoursePageClient courseName="cbse" />
      <section className="px-[20px] py-8 lg:py-8 lg:px-[100px]">
        <TestSeriesSection course_type={[11, 12]} />
      </section>
      <FeaturesSection />
      <HomePageSwiper course="boards" course_name="CBSE" />
      <Faculties course="boards" />
      <BlogsCards category="" course="BOARD" />
      <DownloadBanner />
      <FaqComponent courseName="default" />
      <CBSETestSeriesContent />
    </div>
  );
};

export default CoursePage;
