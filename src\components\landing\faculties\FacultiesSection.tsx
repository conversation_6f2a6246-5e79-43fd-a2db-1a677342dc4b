import FacultyCard from './faculty_card';

type Faculty = {
  name: string;
  description: string;
  faculty_pic_url: string;
  faculty_type: {
    name: string;
    description: string;
    course_type: {
      id: number;
      title: string;
      description: string;
      subjects: {
        id: number;
        title: string;
        description: string;
        course_type: number;
      }[];
      attempts: {
        id: number;
        month: string;
        year: number;
      }[];
    }[];
  };
};

interface FacultiesSectionProps {
  allFaculties: Faculty[];
}

const FacultiesSection: React.FC<FacultiesSectionProps> = ({
  allFaculties,
}) => {
  if (!allFaculties || allFaculties.length === 0) {
    return (
      <section className=" px-[20px] py-8 lg:py-16 lg:pb-8 lg:px-[100px] lg:pt-20">
        <h2 className="my-4 font-primary_medium font-medium text-2xl md:text-3xl lg:text-4xl text-[#1D1D1D]">
          Faculties
        </h2>
        <div className="flex flex-col items-center justify-center min-h-[200px] bg-gray-50 rounded-lg">
          <div className="text-gray-500 text-lg font-medium">
            No Faculties available yet
          </div>
          <p className="text-gray-400 text-sm mt-2">
            Check back later for updates
          </p>
        </div>
      </section>
    );
  }

  return (
    <section className="px-[20px] lg:px-[100px] lg:pt-20">
      <div>
        <h2 className="font-primary_medium text-[36px] mb-2 text-[#1D1D1D]">
          Faculties
        </h2>

        <div className="grid grid-cols-2 gap-4 md:grid-cols-6 md:gap-y-8 md:gap-x-12">
          {allFaculties?.map((faculty) => (
            <FacultyCard
              key={faculty.faculty_pic_url}
              imgSrc={faculty.faculty_pic_url}
              facultyName={faculty.name}
              desc={faculty.description}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default FacultiesSection;
