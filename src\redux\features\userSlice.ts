import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { User } from "@/types/User.ts";
import { RootState } from '@/redux/store.ts';

const initialState: User = {
    userId : null,
    name : '',
    gender : '',
    email : '',
    phoneNumber : '',
    firebaseUID : '',
    emailVerified : null,
    providerApiKey : '',
    tokenResponse : {
        refreshToken : '',
        idToken : '',
        expiresIn : '',
        isNewUser : null,
    },
    authTokens : {
        accessToken : '',
        refreshToken : ''
    }
};

const userSlice = createSlice({
    name : 'user',
    initialState,
    reducers : {
        updateUser(state, action: PayloadAction<Partial<User>>) {
            return {
                ...state,
                ...action.payload,
                tokenResponse: {
                    ...state.tokenResponse,
                    ...action.payload.tokenResponse,
                },
                authTokens : {
                    ...state.authTokens,
                    ...action.payload.authTokens,
                }
            };
        },
        logoutUser: () => {
            return initialState; // Reset the state to initial state on logout
        },
        updateIdToken(state, action: PayloadAction<string>) {
            return {
                ...state,
                tokenResponse: {
                    ...state.tokenResponse,
                    idToken: action.payload,
                },
            };
        }
    }
})

export const selectUser = (state: RootState) => state.user;

export const { updateUser , logoutUser, updateIdToken } = userSlice.actions;
export default userSlice.reducer;