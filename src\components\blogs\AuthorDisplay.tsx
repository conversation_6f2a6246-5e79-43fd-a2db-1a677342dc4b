import { parseWithBR } from '@/lib/utils';
import React from 'react';

interface Author {
    id: number;
    author_name: string;
    author_description: string; // Description is an HTML string
}

interface AuthorDisplayProps {
    blog_authors: Author[];
}

const AuthorDisplay: React.FC<AuthorDisplayProps> = ({ blog_authors }) => {
    return (
        <div className="author-list bg-white p-4 rounded-lg shadow-lg">
            {blog_authors.length > 1 ? (
                <h3 className="text-xl font-extrabold text-gray-800 mb-4">Authors</h3>
            ) : (
                <h3 className="text-xl font-extrabold text-gray-800 mb-4">Author</h3>
            )}

            <ol className="space-y-6">
                {blog_authors.map((author) => (
                    <li key={author.id} className="flex gap-4 items-start">
                        <div className="flex-1">
                            <h2 className="font-semibold text-gray-900 mb-2">{author.author_name}</h2>
                            <p className="text-gray-600 text-xs mb-2">{author.author_description ? parseWithBR(author.author_description) : "No description available"}</p>
                        </div>
                    </li>
                ))}
            </ol>
        </div>
    );
};

export default AuthorDisplay;