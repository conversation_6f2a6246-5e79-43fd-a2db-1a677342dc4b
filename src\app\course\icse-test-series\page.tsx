import CoursePageClient from '@/components/navbar/course/CoursePage';
import { Metadata } from 'next';
import DownloadBanner from '@/components/landing/download/download_banner';
import Faculties from '@/components/landing/faculties/Faculties';
import FeaturesSection from '@/components/landing/features/FeaturesSection';
import HeroImageSection from '@/components/Promotion';
import TestSeriesSection from '@/components/landing/landing_test_series/TestSeries';
import HomePageSwiper from '@/components/landing/swiper/home_page_swiper';
import FilterModal from '@/components/test_series/filter_modal';
import WhatsAppIcon from '@/components/dashboard/whatsApp';
import FaqComponent from '@/components/landing/faq/courseFaq';
import BlogsCards from '@/components/blogs/blogsCard';
import ICSETestSeries from '@/components/course/ICSEContent';

export async function generateMetadata(): Promise<Metadata> {
  let title = '';
  let description = '';
  let keywords = '';
  let url = '';
  let imageUrl = '';

  if (process.env.NEXT_PUBLIC_IS_PROD == 'true') {
    title = 'ICSE/ ISC Board Test Series for Class 10th and 12th | Gradehunt';
    description =
      'Ace your ICSE/ ISC Board exams with our comprehensive test series and mentorship program. Benefit from exam-oriented test papers, practice notes, and study material. Register today!';
    keywords =
      'ICSE Test Series, ICSE Class 10 Test Series, ICSE Class 12 Test Series, ICSE Online Test Series, ICSE Board Preparation, ICSE Practice Papers, Gradehunt Test Series, Class 10th ICSE Preparation, Class 12th ICSE Online Coaching, ICSE 2025 Exam Prep';
    url = 'https://gradehunt.com/course/icse-test-series';
    imageUrl = 'https://gradehunt.com/icons/gradehunt_logo.svg';
  }

  return {
    title,
    description,
    keywords: keywords.split(', '),
    alternates: { canonical: url },
    openGraph: {
      title,
      description:
        'Comprehensive ICSE Class 10 & 12 test series from Gradehunt. Get mentorship, exam-focused test papers, notes, and more.',
      url,
      images: [imageUrl],
      type: 'website',
      locale: 'en_US',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description:
        'Start preparing for ICSE Class 10 & 12 with Gradehunt. Online test series, notes, and mentorship to help you score high.',
      images: [imageUrl],
      site: '@gradehunt',
      creator: '@gradehunt',
    },
    other: {
      rating: 'General',
      Category: 'Education',
      Language: 'en-US',
      distribution: 'global',
      author: 'Gradehunt',
      viewport: 'width=device-width, initial-scale=1.0',
      robots: 'index, follow',
    },
  };
}

const CoursePage = () => {
  return (
    <div>
      <WhatsAppIcon />
      <FilterModal />
      <HeroImageSection course="boards" />
      <CoursePageClient courseName="icse" />
      <section className="px-[20px] py-8 lg:py-8 lg:px-[100px]">
        <TestSeriesSection course_type={[9, 10]} />
      </section>
      <FeaturesSection />
      <HomePageSwiper course="boards" course_name="ICSE" />
      <Faculties course="boards" />
      <BlogsCards category="" course="BOARD" />
      <DownloadBanner />
      <FaqComponent courseName="default" />
      <ICSETestSeries />
    </div>
  );
};

export default CoursePage;
