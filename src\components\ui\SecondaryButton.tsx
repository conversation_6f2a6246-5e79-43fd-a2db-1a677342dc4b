type Props = {
  children: React.ReactNode; // Accept any valid React node as children
  additionalClassNames?: string;
  disabled?: boolean;
}

const SecondaryButton = ({ children, additionalClassNames, disabled }: Props) => {
return (
  <button
    className={`rounded-[33px] text-[20px] text-black font-medium cursor-pointer tracking-[0.5%] bg-[#FFAE1E] py-1.5 px-[24px]
    transition-all duration-150 ${additionalClassNames} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    disabled={disabled}
  >
    {children}
  </button>
)
}

export default SecondaryButton;
