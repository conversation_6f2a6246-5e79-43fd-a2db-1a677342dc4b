'use client';

import LandingTestSeriesCard from './landing_test_series_card';
import MobileSwiperTestSeries from '../swiper/mobile_swiper_test_series';
import { useState } from 'react';
import { FaArrowLeft, FaArrowRight } from 'react-icons/fa'; // For navigation arrows

interface course_type {
  course_group_name?: string;
  title?: string;
}

export type TestSeriesType = {
  id: number;
  title: string;
  description: string;
  course_type: course_type;
  created_at: string;
  updated_at: string;
};

interface TestSeriesSectionProps {
  allTestSeries: TestSeriesType[];
}

const LandingTestSeries: React.FC<TestSeriesSectionProps> = ({
  allTestSeries,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const cardsPerPage = 4;
  const cardsPerTabPage = 3;

  const handlePrevClick = (cardsPerPage: number) => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - cardsPerPage);
    }
  };

  const handleNextClick = (cardsPerPage: number) => {
    if (currentIndex + cardsPerPage < allTestSeries?.length) {
      setCurrentIndex(currentIndex + cardsPerPage);
    }
  };

  const visibleTestSeries = allTestSeries?.slice(
    currentIndex,
    currentIndex + cardsPerPage
  );
  const visibleTabTestSeries = allTestSeries?.slice(
    currentIndex,
    currentIndex + cardsPerTabPage
  );

  if (!allTestSeries || allTestSeries.length === 0) {
    return (
      <div className="relative my-4">
        <h2 className="my-4 font-primary_medium font-medium text-2xl md:text-3xl lg:text-4xl text-[#1D1D1D]">
          Test Series
        </h2>
        <div className="flex flex-col items-center justify-center min-h-[200px] bg-gray-50 rounded-lg">
          <div className="text-gray-500 text-lg font-medium">
            No test series available yet
          </div>
          <p className="text-gray-400 text-sm mt-2">
            Check back later for updates
          </p>
        </div>
      </div>
    );
  }

  console.log(visibleTestSeries);
  return (
    <div className="relative my-4">
      <h2 className="my-4 font-primary_medium font-medium text-2xl md:text-3xl lg:text-4xl text-[#1D1D1D]">
        Test Series
      </h2>

      {/* Desktop Carousel View */}
      <div className="hidden lg:block">
        <div className="grid grid-cols-4 gap-6 w-[90%] mx-auto">
          {visibleTestSeries?.map((testseries, index) => (
            <LandingTestSeriesCard
              key={index}
              title={testseries.title}
              description={testseries.description}
              courseType={testseries.course_type}
            />
          ))}
        </div>

        {/* Navigation Arrows */}
        {allTestSeries?.length > cardsPerPage && (
          <div className="absolute top-1/2 mt-5 transform -translate-y-1/2 w-full flex justify-between px-4">
            <button
              onClick={() => handlePrevClick(cardsPerPage)}
              disabled={currentIndex === 0}
              className={`text-xl p-2 ${
                currentIndex === 0 ? 'opacity-50' : 'hover:bg-gray-200'
              } rounded-full`}
            >
              <FaArrowLeft />
            </button>
            <button
              onClick={() => handleNextClick(cardsPerPage)}
              disabled={currentIndex + cardsPerPage >= allTestSeries?.length}
              className={`text-xl p-2 ${
                currentIndex + cardsPerPage >= allTestSeries?.length
                  ? 'opacity-50'
                  : 'hover:bg-gray-200'
              } rounded-full`}
            >
              <FaArrowRight />
            </button>
          </div>
        )}
      </div>

      {/* Tablet Carousel View */}
      <div className="hidden md:block lg:hidden">
        <div className="grid grid-cols-3 gap-4 w-[90%] mx-auto">
          {visibleTabTestSeries?.map((testseries, index) => (
            <LandingTestSeriesCard
              key={index}
              title={testseries.title}
              description={testseries.description}
            />
          ))}
        </div>

        {/* Navigation Arrows */}
        {allTestSeries?.length > cardsPerTabPage && (
          <div className="absolute top-1/2 mt-5 transform -translate-y-1/2 w-full flex justify-between px-4">
            <button
              onClick={() => handlePrevClick(cardsPerTabPage)}
              disabled={currentIndex === 0}
              className={`text-xl p-2 ${
                currentIndex === 0 ? 'opacity-50' : 'hover:bg-gray-200'
              } rounded-full`}
            >
              <FaArrowLeft />
            </button>
            <button
              onClick={() => handleNextClick(cardsPerTabPage)}
              disabled={currentIndex + cardsPerTabPage >= allTestSeries?.length}
              className={`text-xl p-2 ${
                currentIndex + cardsPerTabPage >= allTestSeries?.length
                  ? 'opacity-50'
                  : 'hover:bg-gray-200'
              } rounded-full`}
            >
              <FaArrowRight />
            </button>
          </div>
        )}
      </div>

      {/* Mobile View */}
      <div className="md:hidden">
        <MobileSwiperTestSeries testData={allTestSeries ?? []} />
      </div>
    </div>
  );
};

export default LandingTestSeries;
