'use client';

import { useEffect, useState } from 'react';
import FAQSection from './faq_section';

interface CoursePageProps {
  courseName: string;
}

function FaqComponent({ courseName }: CoursePageProps) {
  const [ids, setIds] = useState<number[]>([]);

  useEffect(() => {
    const courseFaqMapping: { [key: string]: number[] } = {
      ca: [1, 4, 5, 6],
      cs: [2, 4, 5, 6],
      cma: [3, 4, 5, 6],
      default: [7, 8, 9],
    };
    setIds(courseFaqMapping[courseName]);
  }, []);

  return <FAQSection ids={ids} />;
}

export default FaqComponent;
