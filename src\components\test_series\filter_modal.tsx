'use client';

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import SecondaryButton from '../ui/SecondaryButton';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectParentCourseName,
  setSelectCourseType,
  setSelectedAttempt,
  setSelectedMainCourse,
} from '@/redux/features/selectedFilterModalCourse';
import { closeModal } from '@/redux/features/filterModalSlice.ts';
import { useGetCoursesQuery } from '@/redux/apiSlice';
import { RootState } from '@/redux/store';
import React, { useEffect, useMemo, useState } from 'react';
import { Attempt, CourseData, CourseType } from '@/types/Course';
import { useRouter } from 'next/navigation';
import { usePathname } from 'next/navigation';
import { setSelectedTestSeries } from '@/redux/features/testSeriesSlice.ts';

export default function FilterModal({
  isOpen,
}: {
  isOpen?: boolean;
  onClose?: () => void;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const dispatch = useDispatch();

  const {
    isOpen: modalIsOpen,
    courseGroup,
    category,
  } = useSelector((state: RootState) => state.filterModal);
  const { data: allCourses } = useGetCoursesQuery(courseGroup || undefined);

  // Get parent course name from Redux or derive from category if available
  const parentCourseNameFromState = useSelector(
    (state: RootState) => state.course.selectedParentCourseName
  );

  const mapCategoryToCourseName = (categoryCode: string): string => {
    // Handle specific mappings
    if (categoryCode === 'MAHARASHTRA_BOARD') return 'Maharashtra Board';

    // For other cases, try to convert from SNAKE_CASE to Title Case
    return categoryCode;
  };

  // If we have a category, immediately use it as the parent course name
  const parentCourseName =
    category && modalIsOpen
      ? mapCategoryToCourseName(category)
      : parentCourseNameFromState;

  const selectedCourseType = useSelector(
    (state: RootState) => state.course.selectedCourseType
  );
  const selectedMainCourse = useSelector(
    (state: RootState) => state.course.selectedMainCourse
  );
  const selectedAttempt = useSelector(
    (state: RootState) => state.course.selectedAttempt
  );
  const [radioCourseName, setRadioCourseName] = useState<string | null>(
    parentCourseName ? parentCourseName : selectedMainCourse?.name ?? ''
  );
  const [currentMainCourse, setCurrentMainCourse] = useState<CourseData | null>(
    selectedMainCourse ? selectedMainCourse : null
  );
  const [currentCourseType, setCurrentCourseType] = useState<CourseType | null>(
    selectedCourseType ? selectedCourseType : null
  );
  const [currentAttempt, setCurrentAttempt] = useState<Attempt[] | null>(
    selectedAttempt ? selectedAttempt : []
  );
  const [errors, setErrors] = useState<string>('');

  const filteredCourses = useMemo(() => {
    if (!allCourses?.results) return [];

    if (selectedMainCourse?.course_group_name) {
      return allCourses.results.filter(
        (course) =>
          course.course_group_name === selectedMainCourse.course_group_name
      );
    }
    return allCourses.results;
  }, [allCourses, selectedMainCourse]);

  // Pre-select course based on category when modal opens
  useEffect(() => {
    if (allCourses?.results && category && modalIsOpen) {
      // Convert category to expected course name format

      const expectedCourseName = mapCategoryToCourseName(category);

      // Log for debugging
      console.log(
        'Category:',
        category,
        'Expected Course Name:',
        expectedCourseName
      );

      const matchingCourse = allCourses.results.find(
        (course) => course.name === expectedCourseName
      );

      if (matchingCourse) {
        console.log('Found matching course:', matchingCourse.name);
        // Set the parent course name immediately
        dispatch(selectParentCourseName(expectedCourseName));
        dispatch(setSelectedMainCourse(matchingCourse));

        if (matchingCourse.course_type.length > 0) {
          dispatch(setSelectCourseType(matchingCourse.course_type[0]));

          if (matchingCourse.course_type[0].attempts.length > 0) {
            dispatch(
              setSelectedAttempt([matchingCourse.course_type[0].attempts[0]])
            );
          }
        }

        // Update local state immediately as well
        setRadioCourseName(expectedCourseName);
        setCurrentMainCourse(matchingCourse);
      } else {
        console.log('No matching course found for:', expectedCourseName);
        // If no match found, try to use the category directly
        dispatch(selectParentCourseName(category));
        setRadioCourseName(category);
      }
    }
  }, [allCourses, category, modalIsOpen, dispatch]);

  // Update state based on course changes
  useEffect(() => {
    if (allCourses && parentCourseName) {
      setRadioCourseName(parentCourseName);
      setCurrentCourseType(selectedCourseType);
      setCurrentAttempt(selectedAttempt || []);
      const selectedCourse = allCourses.results.find(
        (course) => course.name === parentCourseName
      );
      if (selectedCourse) {
        setCurrentMainCourse(selectedCourse);
      }
    }
  }, [allCourses, parentCourseName, selectedCourseType, selectedAttempt]);

  // Auto-select first attempt when course type changes
  useEffect(() => {
    if (currentCourseType && currentCourseType.attempts.length > 0) {
      dispatch(setSelectedAttempt([currentCourseType.attempts[0]])); // Auto-select the first attempt
    }
  }, [currentCourseType, dispatch]);

  // Handle course radio change
  const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const courseName = event.currentTarget.value;
    const selectedCourse = allCourses?.results.find(
      (course) => course.name === courseName
    );
    if (selectedCourse) {
      dispatch(selectParentCourseName(courseName));
      dispatch(setSelectedMainCourse(selectedCourse));
      dispatch(setSelectCourseType(selectedCourse.course_type[0]));
      dispatch(setSelectedAttempt(selectedCourse.course_type[0].attempts));
    }
  };

  // Handle course type radio change
  const handleCourseTypeRadioChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const courseTypeName = event.currentTarget.value;
    const selectedCourseType = selectedMainCourse?.course_type.find(
      (type) => type.title === courseTypeName
    );
    dispatch(setSelectCourseType(selectedCourseType || null));
    dispatch(
      setSelectedAttempt(
        selectedCourseType?.attempts ? [selectedCourseType.attempts[0]] : []
      )
    ); // Select first attempt by default
  };

  const handleCloseModal = () => {
    setCurrentMainCourse(selectedMainCourse);
    setCurrentCourseType(selectedCourseType);
    setCurrentAttempt(selectedAttempt ? selectedAttempt : []);
    setRadioCourseName(parentCourseName);
    setErrors('');
    dispatch(closeModal());
  };
  console.log(parentCourseName, selectedCourseType);
  const handleSubmitNext = () => {
    dispatch(setSelectedTestSeries(null));
    setErrors('');
    if (
      !currentCourseType ||
      currentAttempt?.length === 0 ||
      !currentMainCourse
    ) {
      setErrors('Please select all the fields to continue');
      return;
    }
    dispatch(selectParentCourseName(radioCourseName));
    dispatch(setSelectedAttempt(currentAttempt ?? []));
    dispatch(setSelectCourseType(currentCourseType));
    dispatch(setSelectedMainCourse(currentMainCourse));
    const attemptIds = currentAttempt?.map((attempt) => attempt.id).join(',');
    const cleanedAttemptIds = attemptIds?.startsWith(',')
      ? attemptIds.slice(1)
      : attemptIds;
    router.push(
      `/test-series/filter?coursetypeid=${currentCourseType.id}&attemptid=${cleanedAttemptIds}`
    );
    dispatch(closeModal());
  };

  return (
    <Dialog open={isOpen || modalIsOpen} onOpenChange={handleCloseModal}>
      <DialogContent
        className="sm:max-w-sm md:max-w-[450px]"
        aria-describedby=""
      >
        <DialogHeader>
          <DialogTitle className="font-primary_medium text-[#1D1D1D] font-medium text-base">
            Select your course details
          </DialogTitle>
        </DialogHeader>
        <div className="flex flex-col space-x-2 border-b-[1px] pb-4 border-b-[#CAC4D0]">
          <p className="font-primary_medium text-[#1D1D1D] font-medium text-sm">
            Course
          </p>
          <div className="grid grid-cols-3 gap-x-4 gap-y-6 my-4">
            {filteredCourses.map((course, index) => (
              <div className="flex items-center space-x-2" key={index}>
                <input
                  type="radio"
                  value={course.name}
                  checked={parentCourseName === course.name}
                  onChange={handleRadioChange}
                  id={`course-${course.name}`}
                  className="w-2 h-2 p-2 cursor-pointer checked:bg-[#FFAE1E] checked:focus:bg-[#FFAE1E] checked:bg-none bg-white focus:ring-white border-[#3F3F3F] checked:border-[#FFAE1E]"
                />
                <Label
                  htmlFor={`course-${course.name}`}
                  className="text-sm whitespace-normal break-words"
                >
                  {course.name}
                </Label>
              </div>
            ))}
          </div>
        </div>
        <div className="flex flex-col space-x-2 border-b-[1px] pb-4 border-b-[#CAC4D0]">
          <p className="font-primary_medium text-[#1D1D1D] font-medium text-sm">
            Course type
          </p>
          <div className="grid grid-cols-2 gap-y-4 my-4">
            {currentMainCourse?.course_type.map((courseType, index) => (
              <div
                className="flex items-center space-x-2 min-w-[115px]"
                key={index}
              >
                <input
                  type="radio"
                  value={courseType.title}
                  checked={selectedCourseType?.title === courseType.title}
                  onChange={handleCourseTypeRadioChange}
                  id={`course-type-${courseType.title}`}
                  className="w-2 h-2 p-2 cursor-pointer checked:bg-[#FFAE1E] checked:hover:bg-[#FFAE1E] checked:focus:bg-[#FFAE1E] focus:bg-[#FFAE1E] checked:bg-none bg-white focus:ring-[#FFAE1E] border-[#3F3F3F] checked:border-[#FFAE1E]"
                />
                <Label
                  htmlFor={`course-type-${courseType.title}`}
                  className="text-sm"
                >
                  {courseType.title}
                </Label>
              </div>
            ))}
          </div>
        </div>
        <div className="flex flex-col space-x-2 border-b-[1px] pb-4 border-b-[#CAC4D0]">
          <p className="font-primary_medium text-[#1D1D1D] font-medium text-sm">
            Attempt
          </p>
          <div className="grid grid-cols-2 gap-y-4 my-4">
            {currentCourseType?.attempts.map((attempt, index) => (
              <div
                className="flex items-center space-x-2 w-[120px]"
                key={index}
              >
                <input
                  type="radio"
                  value={attempt.id.toString()}
                  checked={
                    selectedAttempt && selectedAttempt[0]?.id === attempt.id
                  } // Check if this attempt is selected
                  onChange={() => {
                    // Dispatch the selected attempt
                    dispatch(setSelectedAttempt([attempt])); // Set the selected attempt as an array with the current attempt
                  }}
                  id={`attempt-${attempt.id}`}
                  className="w-4 h-4 cursor-pointer rounded-full appearance-none border-2 border-[#FFAE1E] checked:bg-[#FFAE1E] checked:border-[#FFAE1E] focus:outline-none relative before:content-[''] before:absolute before:top-0 before:left-0 before:right-0 before:bottom-0 before:rounded-full before:bg-[#FFAE1E] before:scale-0 checked:before:scale-90 before:transition-transform"
                />
                <Label htmlFor={`attempt-${attempt.id}`} className="text-sm">
                  {attempt.month} {attempt.year}
                </Label>
              </div>
            ))}
          </div>
        </div>
        <p className="font-primary_medium text-sm text-red-600">{errors}</p>
        <DialogFooter className="sm:justify-start w-full">
          <div onClick={handleSubmitNext} className="w-full">
            <SecondaryButton additionalClassNames="w-full">
              {pathname === '/test-series/filter' ? 'Apply Filter' : 'Next'}
            </SecondaryButton>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
