import React from "react";
import { motion } from "framer-motion";

const FAQItem = ({ id, question, answer, openAccordion, toggleAccordion }: { id: number, question: string, answer: React.ReactNode, openAccordion: number | null, toggleAccordion: (id: number) => void; }) => {
  const isOpen = openAccordion === id;

  return (
    <div>
      <h2 id={`accordion-heading-${id}`} className={id !== 1 ? "mt-4" : ""}>
        <button
          type="button"
          className="flex items-center justify-between w-full p-3 font-medium text-[#3F3F3F] rounded-xl bg-[#EEEEEE] gap-3"
          onClick={() => toggleAccordion(id)}
          aria-expanded={isOpen}
          aria-controls={`accordion-body-${id}`}
        >
          <span>{question}</span>
          <svg
            data-accordion-icon
            className={`w-3 h-3 shrink-0 transition-transform duration-300 ease-in-out ${isOpen ? 'rotate-180' : ''}`}
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 10 6"
          >
            <path
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M9 5 5 1 1 5"
            />
          </svg>
        </button>
      </h2>
      <motion.div
        id={`accordion-body-${id}`}
        initial={false}
        animate={{ height: isOpen ? 'auto' : 0 }}
        style={{ overflow: 'hidden' }}
        aria-labelledby={`accordion-heading-${id}`}
        transition={{ duration: 0.3, ease: "easeInOut" }}
      >
        <div className="p-5 rounded-b-xl bg-[#EEEEEE] text-[#3F3F3F]">
          {answer}
        </div>
      </motion.div>
    </div>
  );
};

export default FAQItem;
