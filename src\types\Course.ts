export interface CourseData {
  course_group_name: string;
  is_professional_course: boolean;
  name: string;
  description: string;
  course_type: CourseType[];
}

export interface CourseType {
  id: number;
  title: string;
  description: string;
  subjects: Subject[];
  attempts: Attempt[];
}

export interface Subject {
  id: number;
  title: string;
  description: string;
  course_type: number;
}

export interface Attempt {
  id: number;
  month: string;
  year: number;
}


