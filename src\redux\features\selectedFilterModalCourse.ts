import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Attempt, CourseData, CourseType } from '@/types/Course';

interface CourseState {
  selectedMainCourse: CourseData | null;
  selectedCourseType: CourseType | null;
  selectedParentCourseName: string | null,
  selectedAttempt: Attempt[];
}

const initialState: CourseState = {
  selectedMainCourse: null,
  selectedCourseType: null,
  selectedParentCourseName: null,
  selectedAttempt: [],
};

const selectedFilterModalCourseSlice = createSlice({
  name: 'course',
  initialState,
  reducers: {
    setSelectedMainCourse: (state, action: PayloadAction<CourseData | null>) => {
      state.selectedMainCourse = action.payload
    },
    setSelectCourseType: (state, action: PayloadAction<CourseType | null>) => {
      state.selectedCourseType = action.payload;
    },
    clearSelectedCourse: (state) => {
      state.selectedCourseType = null;
      state.selectedParentCourseName = null;
    },
    selectParentCourseName: (state, action: PayloadAction<string | null>) => {
      state.selectedParentCourseName = action.payload
    },
    setSelectedAttempt: (state, action: PayloadAction<Attempt[]>) => {
      state.selectedAttempt = action.payload;
    },
  },
});

export const { setSelectCourseType, clearSelectedCourse, selectParentCourseName, setSelectedAttempt, setSelectedMainCourse } = selectedFilterModalCourseSlice.actions;

export default selectedFilterModalCourseSlice.reducer;
