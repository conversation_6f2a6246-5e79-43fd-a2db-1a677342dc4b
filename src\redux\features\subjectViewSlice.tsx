import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define the initial state
export interface SubjectViewState {
  isSubjectView: boolean;
}

const initialState: SubjectViewState = {
  isSubjectView: false,
};

// Create the slice
const subjectViewSlice = createSlice({
  name: 'subjectView',
  initialState,
  reducers: {
    toggleSubjectView: (state, action: PayloadAction<boolean>) => {
      state.isSubjectView = action.payload;
    },
  },
});

// Export the action creator
export const { toggleSubjectView } = subjectViewSlice.actions;

// Export the reducer
export default subjectViewSlice.reducer;