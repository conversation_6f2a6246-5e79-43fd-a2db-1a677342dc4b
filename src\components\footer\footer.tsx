'use client';

import Link from 'next/link';
import {
  FaPhoneAlt,
  FaEnvelope,
  FaFacebookF,
  FaInstagram,
  FaTwitter,
  FaLinkedin,
  FaYoutube,
} from 'react-icons/fa';

const Footer = () => {
  return (
    <footer className="absolute w-full px-[30px] lg:px-[71px] py-12 bg-[#1D1D1D] font-primary text-[#FFAE1E] flex flex-col lg:flex-row lg:gap-0 gap-6 grid grid-cols-12 z-[49]">
      {/* Logo Section */}
      <div className="col-span-12 lg:col-span-2 flex justify-between items-center lg:block lg:pb-0 pb-2">
        <img
          src="/icons/gradehunt_logo.svg"
          alt="Gradehunt Logo"
          loading="lazy"
          className="w-[68px] h-[68px] lg:w-[98px] lg:h-[98px]"
        />
      </div>

      {/* Links Section */}
      <div className="col-span-12 lg:col-span-9 text-[#FFAE1E] font-medium text-[16px] lg:text-base grid grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-3 lg:gap-x-16">
        {/* Courses */}
        <div>
          <p className="text-[#FFAE1E] mb-2 font-semibold">Courses</p>
          <ul className="text-[#FFFFFF] text-[14px] space-y-1">
            <li>
              <Link href="/course/ca-test-series">CA Final</Link>
            </li>
            <li>
              <Link href="/course/ca-test-series">CA Inter</Link>
            </li>
            <li>
              <Link href="/course/ca-test-series">CA Foundation</Link>
            </li>
            <li>
              <Link href="/course/cs-test-series">CSEET</Link>
            </li>
            <li>
              <Link href="/course/cs-test-series">CS Executive</Link>
            </li>
            <li>
              <Link href="/course/cs-test-series">CS Professional</Link>
            </li>
            <li>
              <Link href="/course/cma-test-series">CMA Inter</Link>
            </li>
            <li>
              <Link href="/course/cma-test-series">CMA Final</Link>
            </li>
          </ul>
        </div>

        {/* Quick Links */}
        <div>
          <p className="text-[#FFAE1E] mb-2 font-semibold">Quick Links</p>
          <ul className="text-[#FFFFFF] text-[14px] space-y-1">
            <li>
              <Link href="/">Home</Link>
            </li>
            <li>
              <Link href="/privacy-policy">Privacy Policy</Link>
            </li>
            <li>
              <Link href="/terms-and-conditions">Terms & Conditions</Link>
            </li>
            <li>
              <Link href="/refund-and-cancellation">
                Refund and Cancellation
              </Link>
            </li>
            <li>
              <Link href="/#faculties">Our Faculty</Link>
            </li>
            <li>
              <Link href="/#faqs">FAQs</Link>
            </li>
            <li>
              <Link href="/contact-us">Contact Us</Link>
            </li>
            <li>
              <Link href="/sitemap.xml">Sitemap</Link>
            </li>
          </ul>
        </div>

        {/* Contact Info */}
        <div className="col-span-2 lg:col-span-1 text-[#FFFFFF] text-[14px] mt-6 lg:mt-0">
          <p className="text-[#FFAE1E] mb-2 font-semibold">Contact Us</p>
          <div className="flex items-center space-x-2">
            <FaPhoneAlt />
            <span>+91-8808800320</span>
          </div>
          <div className="flex items-center space-x-2 mt-2">
            <FaEnvelope />
            <a href="mailto:<EMAIL>" className="underline">
              <EMAIL>
            </a>
          </div>
          <div className="flex space-x-4 mt-4 text-[#FFAE1E]">
            <a
              href="https://www.facebook.com/gradehuntcacscma"
              target="_blank"
              rel="noopener noreferrer"
            >
              <FaFacebookF size={20} />
            </a>
            <a
              href="https://www.instagram.com/gradehunt_ca_cs_cma"
              target="_blank"
              rel="noopener noreferrer"
            >
              <FaInstagram size={20} />
            </a>
            <a
              href="https://x.com/gradehunt"
              target="_blank"
              rel="noopener noreferrer"
            >
              <FaTwitter size={20} />
            </a>
            <a
              href="https://www.linkedin.com/company/gradehunt/"
              target="_blank"
              rel="noopener noreferrer"
            >
              <FaLinkedin size={20} />
            </a>
            <a
              href="https://youtube.com/@gradehunt?si=C4vxA-hlyvafyPRW"
              target="_blank"
              rel="noopener noreferrer"
            >
              <FaYoutube size={20} />
            </a>
          </div>
        </div>
      </div>

      {/* Footer Note */}
      <p className="text-[#FFFFFF] text-[11px] mt-auto col-span-12 text-center lg:text-left lg:mt-0">
        All rights reserved
      </p>
    </footer>
  );
};

export default Footer;

{
  /* <div className="text-[#FFFFFF] text-[16px] leading-[24px] font-normal  ">
        <div className="flex justify-start items-center gap-2">
          <FaApple size={16} />
          Download For IOS
        </div>
        <div className="flex justify-start items-center gap-2">
          <FaPlay size={14} />
          Download For Android
        </div>
      </div> */
}
