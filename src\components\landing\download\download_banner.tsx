import { FaApple, FaPlay } from "react-icons/fa";

const DownloadBanner = () => {
  return (
    <div className="hidden bg-gradient-to-r from-yellow-400 to-[#FFAE1E] flex flex-col lg:gap-12 gap-8 lg:px-16 px-8 justify-center items-center min-h-[300px] rounded-lg shadow-lg">
      {/* Heading Section */}
      <h2 className="text-[#1D1D1D] font-bold text-2xl md:text-3xl lg:text-4xl tracking-wide text-center">
        Get Our Mobile App
      </h2>

      {/* Download Buttons Section */}
      <div className="flex flex-col md:flex-row gap-6 mt-4">
        {/* iOS Button */}
        <button className="bg-white text-[#1D1D1D] flex items-center gap-3 px-6 py-3 rounded-full shadow-md hover:bg-gray-100 transition duration-300">
          <FaApple className="text-2xl" /> 
          <span className="text-lg font-semibold">Download for iOS</span>
        </button>

        {/* Android Button */}
        <button className="bg-white text-[#1D1D1D] flex items-center gap-3 px-6 py-3 rounded-full shadow-md hover:bg-gray-100 transition duration-300">
          <FaPlay className="text-2xl" /> 
          <span className="text-lg font-semibold">Download for Android</span>
        </button>
      </div>
    </div>
  );
};

export default DownloadBanner;
