import CoursePageClient from '@/components/navbar/course/CoursePage';
import { Metadata } from 'next';
import DownloadBanner from '@/components/landing/download/download_banner';
import Faculties from '@/components/landing/faculties/Faculties';
import FeaturesSection from '@/components/landing/features/FeaturesSection';
import HeroImageSection from '@/components/Promotion';
import TestSeriesSection from '@/components/landing/landing_test_series/TestSeries';
import HomePageSwiper from '@/components/landing/swiper/home_page_swiper';
import FilterModal from '@/components/test_series/filter_modal';
import WhatsAppIcon from '@/components/dashboard/whatsApp';
import FaqComponent from '@/components/landing/faq/courseFaq';
import CSTestSeriesContent from '@/components/course/CsContent';
import BlogsCards from '@/components/blogs/blogsCard';

export async function generateMetadata(): Promise<Metadata> {
  let title = '';
  let description = '';
  let keywords = '';
  let url = '';
  let imageUrl = '';

  if (process.env.NEXT_PUBLIC_IS_PROD == 'true') {
    title = 'CS Test Series | CS Professional, Executive, CSEET | Gradehunt';
    description =
      'We offer test series for all 3 CS levels: CSEET, CS Executive, and CS Professional. Ace your exams with professional papers, detailed feedback, and personalized doubt-solving.';
    keywords =
      'CS Test Series, CS Professional Test Series, CS Executive Test Series, CSEET';
    url = 'https://gradehunt.com/course/cs-test-series';
    imageUrl = 'https://gradehunt.com/icons/gradehunt_logo.svg';
  }

  return {
    title,
    description,
    keywords: keywords.split(', '),
    alternates: { canonical: url },
    openGraph: {
      title,
      description,
      url,
      images: [imageUrl],
      type: 'website',
      locale: 'en_US',
    },
    twitter: {
      card: 'summary',
      title,
      description,
      images: [imageUrl],
      site: '@gradehunt',
      creator: '@gradehunt',
    },
    other: {
      rating: 'General',
      Category: 'Education',
      Language: 'en-US',
      distribution: 'global',
    },
  };
}

const CoursePage = () => {
  return (
    <div>
      <WhatsAppIcon />
      <FilterModal />
      <HeroImageSection course="professional" />
      <CoursePageClient courseName="cs" />
      <section className="px-[20px] py-8 lg:py-8 lg:px-[100px]">
        <TestSeriesSection course_type={[5, 6]} />
      </section>
      <FeaturesSection />
      <HomePageSwiper course="professional" course_name="CS" />
      <Faculties course="professional" />
      <BlogsCards category="" course="PROFESSIONAL" />
      <DownloadBanner />
      <FaqComponent courseName="cs" />
      <CSTestSeriesContent />
    </div>
  );
};

export default CoursePage;
