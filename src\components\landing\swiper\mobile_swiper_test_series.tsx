'use client';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Swiper as SwiperClass } from 'swiper/types';
import { Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import './swiperstyles.css';
import { useCallback, useEffect, useState } from 'react';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import TestSeriesSectionCard from '@/components/landing/landing_test_series/landing_test_series_card';
import { TestSeriesType } from '../landing_test_series/landing_test_series';

const MobileSwiperTestSeries = ({
  testData,
}: {
  testData: TestSeriesType[];
}) => {
  const [swiperRef, setSwiperRef] = useState<SwiperClass>();
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);
  const [domLoaded, setDomLoaded] = useState(false);

  useEffect(() => {
    setDomLoaded(true);
  }, []);

  const handlePrevious = useCallback(() => {
    swiperRef?.slidePrev();
  }, [swiperRef]);

  const handleNext = useCallback(() => {
    swiperRef?.slideNext();
  }, [swiperRef]);

  const handleSwiper = useCallback((swiper: SwiperClass) => {
    setSwiperRef(swiper);
    setIsBeginning(swiper.isBeginning);
    setIsEnd(swiper.isEnd);

    swiper.on('slideChange', () => {
      setIsBeginning(swiper.isBeginning);
      setIsEnd(swiper.isEnd);
    });
  }, []);

  return (
    <>
      {!isBeginning && (
        <div
          className={`bg-[#EEEEEE] flex justify-center items-center w-[24px] h-[24px] sm:w-[60px] sm:h-[60px] lg:w-[80px] lg:h-[80px] border-[0.5px] border-[#CDCDCD] rounded-[50%] absolute right-8 top-2 ${
            isBeginning ? 'opacity-60 cursor-not-allowed' : ''
          }`}
        >
          <button
            onClick={handlePrevious}
            className="text-[#3F3F3F] w-[10px] h-[10px] flex items-center justify-center"
            disabled={isBeginning}
          >
            <FaChevronLeft size={32} />
          </button>
        </div>
      )}

      <div
        className={`bg-[#EEEEEE] flex justify-center items-center w-[24px] h-[24px] sm:w-[60px] sm:h-[60px] lg:w-[80px] lg:h-[80px] border-[0.5px] border-[#CDCDCD] rounded-[50%] absolute right-0 top-2 ${
          isEnd ? 'opacity-60' : ''
        }`}
      >
        <button
          onClick={handleNext}
          className="text-[#3F3F3F] w-[10px] h-[10px] flex items-center justify-center"
        >
          <FaChevronRight size={32} />
        </button>
      </div>
      {domLoaded && (
        <Swiper
          navigation={true}
          modules={[Navigation]}
          className="mySwiper"
          slidesPerView={1}
          spaceBetween={0}
          onSwiper={handleSwiper}
        >
          {testData.map((testseries, index) => (
            <SwiperSlide key={index}>
              <TestSeriesSectionCard
                key={index}
                title={testseries.title}
                description={testseries.description}
              />
            </SwiperSlide>
          ))}
        </Swiper>
      )}
    </>
  );
};

export default MobileSwiperTestSeries;
