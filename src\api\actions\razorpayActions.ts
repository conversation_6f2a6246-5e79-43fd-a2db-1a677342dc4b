import { baseUrl } from "@/config/constants";


export const createRazorpayOrder = async (options : { amount : number , currency : string,user_id : number , order_id: number} , headerToken : string | null) => {
    const { amount, currency ,user_id,order_id} = options;

    try {
        const response = await fetch(`${baseUrl}/create-razorpay-session/`, {
            method: 'POST',
             headers: {
                    'Authorization': `${headerToken}`, 
                    'Content-Type': 'application/json', 
                },
            body: JSON.stringify({
                amount,
                currency,
                user_id,
                order_id
            }),
        });

        if (!response.ok) {
            throw new Error('Failed to create Razorpay order');
        }

        return await response;
    } catch (error) {
        console.error('Error creating Razorpay Order API :', error);
        throw error;
    }
};

export const verifyRazorpayTransaction = async({razorpay_payment_id , razorpay_order_id , razorpay_signature , headerToken} : {razorpay_payment_id : string, razorpay_order_id : string , razorpay_signature : string , headerToken : string | null}) => {
    // /verify-razorpay-transaction/
    try {
        const response = await fetch(`${baseUrl}/verify-razorpay-transaction/`, {
            method: 'POST',
             headers: {
                    'Authorization': `${headerToken}`, 
                    'Content-Type': 'application/json', 
                },
            body: JSON.stringify({
                razorpay_payment_id,
                razorpay_order_id,
                razorpay_signature
            }),
        });

        if (!response.ok) {
            throw new Error('Failed to verify Razorpay order');
            console.error(response)
        }

        return await response;
    } catch (error) {
        console.error('Error creating Razorpay Order API :', error);
        throw error;
    }
}