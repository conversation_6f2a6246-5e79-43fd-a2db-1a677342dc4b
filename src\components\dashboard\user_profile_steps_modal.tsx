import { useEffect, useRef, useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import SecondaryButton from '../ui/SecondaryButton';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import {
  useCreateUserAuthMutation,
  useUpdateUserDetailsMutation,
} from '@/redux/apiSlice';
import ClipLoader from 'react-spinners/ClipLoader';
import { baseUrl } from '@/config/constants';
import { updateUser } from '@/redux/features/userSlice';
import { RecaptchaVerifier, signInWithPhoneNumber } from 'firebase/auth';
import { auth } from '@/data/firebase';
import toast from 'react-hot-toast';
import { useUserData } from '@/lib/hooks/useUserData';

declare const window: any;

const UserProfileStepsModal = ({
  isOpen,
  onClose,
  setIsUserProfileModalOpen,
}: {
  isOpen: boolean;
  onClose: any;
  setIsVerificationModalOpen: any;
  closeUserProfileModal: any;
  setIsUserProfileModalOpen: any;
}) => {
  const user = useSelector((state: RootState) => state.user);
  const { isLoading } = useUserData();
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [phoneNum, setPhoneNum] = useState(user.phoneNumber);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [updateUserAPI] = useUpdateUserDetailsMutation();
  const [createUserAuth] = useCreateUserAuthMutation();
  const [loading, setLoading] = useState(false);
  const [userConfirmation, setUserConfirmation] = useState<any>(null);
  const [spinnerColor] = useState('#30b954');
  const [showOTPSection, setShowOTPSection] = useState(false);
  const [OTP, setOTP] = useState<string[]>(Array(6).fill(''));
  const [isOTPSentLoading, setIsOTPSentLoading] = useState(false);
  const [isProfileUpdateLoading, setIsProfileUpdateLoading] = useState(false);
  const [timer, setTimer] = useState(60);
  const [resending, setResending] = useState(false);

  const length = 6;
  const inputRef = useRef<HTMLInputElement[]>(Array(length).fill(null));
  const dispatch = useDispatch();

  useEffect(() => {
    setFullName(user.name);
    setEmail(user.email);
  }, [user]);

  const validateInputs = () => {
    const newErrors: { [key: string]: string } = {};

    if (!fullName) newErrors.fullName = 'Full name is required';
    if (!email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email address is invalid';
    }
    if (!phoneNum) {
      newErrors.phoneNum = 'Phone number is required';
    } else if (!/^\d{10}$/.test(phoneNum)) {
      newErrors.phoneNum = 'Enter valid 10 digit phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleGetAccessToken = async () => {
    const bodyData = {
      phone: phoneNum,
    };
    const { data: accessRes } = await createUserAuth({ body: bodyData });

    if (accessRes) {
      dispatch(
        updateUser({
          userId: accessRes.user_id,
          authTokens: {
            accessToken: accessRes.access,
            refreshToken: accessRes.refresh,
          },
          phoneNumber: phoneNum,
        })
      );
      handleSubmit(accessRes.access);
    }
    return [accessRes.access, accessRes.user_id];
  };

  const handleSubmit = async (token: string) => {
    // if ((user.firebaseUID || user.firebaseUID.length !== 0) && validateInputs()) {
    //   setIsProfileUpdateLoading(true);
    //   setIsOTPSentLoading(true);
    //   try {
    //     await updateUserAPI({
    //       userId: String(user.userId),
    //       name: fullName,
    //       email: email,
    //       phone: phoneNum,
    //       headerToken: user.tokenResponse.idToken,
    //     }).unwrap();
    //     dispatch(
    //       updateUser({
    //         userId: user.userId,
    //         phoneNumber: phoneNum,
    //         email: email,
    //         name: fullName,
    //       })
    //     );
    //     setIsVerificationModalOpen(true);
    //   } catch (error) {
    //     toast.error("Error, please try again");
    //     console.error('Error while updating profile details');
    //   } finally {
    //     setLoading(false);
    //     setIsOTPSentLoading(false);
    //     setIsProfileUpdateLoading(false);
    //   }
    //   return;
    // }

    if (validateInputs()) {
      setLoading(true);
      setIsProfileUpdateLoading(true);
      setIsOTPSentLoading(true);
      try {
        const updateUserApiResponse = await updateUserAPI({
          userId: String(user.userId),
          phone: phoneNum,
          headerToken: token,
          name: fullName,
          email,
        }).unwrap(); // Unwrap will throw an error if the request fails
        if (user.firebaseUID) {
          toast('Profile Updated');
          setShowOTPSection(false);
          setIsUserProfileModalOpen(false);
          dispatch(
            updateUser({
              userId: user.userId,
              phoneNumber: phoneNum,
              firebaseUID: user.firebaseUID,
              email: email,
              name: fullName,
            })
          );
          return;
        } else if (updateUserApiResponse) {
          await handleSendOTP();
        }
      } catch (error: any) {
        if (error?.status === 403) {
          handleGetAccessToken();
        } else {
          console.error('An unexpected error occurred.');
          // Optionally handle other errors
        }
      } finally {
        setIsProfileUpdateLoading(false);
        setIsOTPSentLoading(false);
        setLoading(false);
      }
    }
  };

  const handleSendOTP = async () => {
    try {
      setIsOTPSentLoading(true);
      // if (!window.recaptchaVerifier) {
      if (!resending && !window.recaptchaVerifier) {
        window.recaptchaVerifier = new RecaptchaVerifier(auth, 'recaptcha', {
          size: 'invisible', // Invisible reCAPTCHA
          callback: () => {
            // console.log('Invisible reCAPTCHA solved:', response);
          },
          'expired-callback': () => {
            console.log('Invisible reCAPTCHA expired, please try again.');
          },
        });
      }
      // }
      const appVerifier = window.recaptchaVerifier;
      setIsOTPSentLoading(true);
      setResending(true);
      setTimer(60);

      const phoneNumber = '+91' + phoneNum;
      const result = await signInWithPhoneNumber(
        auth,
        phoneNumber,
        appVerifier
      );
      setUserConfirmation(result);
      toast.success('OTP Sent Successfully');
      setShowOTPSection(true);

      const countdown = setInterval(() => {
        setTimer((prev) => {
          if (prev === 1) {
            clearInterval(countdown);
            setResending(false);
            return 30;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(countdown);
    } catch (error) {
      console.error('Error during OTP sending:', error);
      toast.error('Error during sending OTP, try again!');
    } finally {
      setIsOTPSentLoading(false);
    }
  };

  const handleResendOTP = () => {
    handleSendOTP();
  };

  useEffect(() => {
    return () => {
      if (window.recaptchaVerifier) {
        window.recaptchaVerifier = null;
      }
    };
  }, []);

  const handleReferralProfile = async (accessToken: string) => {
    try {
      const response = await fetch(`${baseUrl}/referral-profile/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: accessToken,
        },
      });

      if (response.ok) {
        const referralData = await response.json();
        console.log('Referral profile:', referralData);
        return referralData;
      } else {
        const errorData = await response.json();
        console.error('Failed to get or create referral profile:', errorData);
        throw new Error('Failed to get or create referral profile');
      }
    } catch (error) {
      console.error('Error in handling referral profile:', error);
      toast.error('Failed to setup referral profile');
      throw error;
    }
  };

  const verifyOTP = async (otp: string) => {
    setIsOTPSentLoading(true);

    const numberWithoutCountryCode = phoneNum;
    try {
      const otpData = await userConfirmation.confirm(otp);
      if (otpData) {
        const bodyData = {
          phone: phoneNum,
          firebase_uid: otpData.user.uid,
        };
        const { data: res } = await createUserAuth({ body: bodyData });
        if (res) {
          await updateUserAPI({
            userId: String(res.user_id),
            phone: numberWithoutCountryCode,
            firebase_uid: otpData.user.uid,
            headerToken: res.access,
          }).unwrap();

          const userRes = await fetch(`${baseUrl}/users/${res.user_id}`, {
            headers: {
              Authorization: res.access,
            },
          });
          const userDetails = await userRes.json();

          if (userRes.status === 200 || userRes.status === 201) {
            try {
              await handleReferralProfile(res.access);
            } catch (referralError) {
              console.error('Referral profile setup failed:', referralError);
            }
            toast('Profile Updated');
            setShowOTPSection(false);
            setIsUserProfileModalOpen(false);
            dispatch(
              updateUser({
                userId: res.user_id,
                phoneNumber: otpData.user.phoneNumber,
                firebaseUID: otpData.user.uid,
                emailVerified: otpData.user.emailVerified,
                providerApiKey: otpData.user.apiKey,
                tokenResponse: {
                  refreshToken: otpData._tokenResponse.refreshToken,
                  idToken: otpData._tokenResponse.idToken,
                  expiresIn: otpData._tokenResponse.expiresIn,
                  isNewUser: otpData._tokenResponse.isNewUser,
                },
                authTokens: {
                  accessToken: res.access,
                  refreshToken: res.refresh,
                },
                email: userDetails.email,
                name: userDetails.name,
              })
            );
            return;
          }
        }
      }
    } catch (err) {
      toast.error('Wrong OTP, Please Try Again');
      console.error(err);
    } finally {
      setIsOTPSentLoading(false);
    }
  };

  const handleTextChange = (input: string, index: number) => {
    // If more than one character is pasted (like 123456)
    if (input.length > 1) {
      const newPin = [...OTP];
      input.split('').forEach((char, idx) => {
        if (index + idx < length) {
          newPin[index + idx] = char;
        }
      });
      setOTP(newPin);

      // Move focus to the last filled input
      const nextIndex = Math.min(index + input.length, length - 1);
      inputRef.current[nextIndex]?.focus();
    } else {
      // Handle single-character input
      if (/^[0-9]$/.test(input) || input === '') {
        const newPin = [...OTP];
        newPin[index] = input;
        setOTP(newPin);

        if (input.length === 1 && index < length - 1) {
          inputRef.current[index + 1]?.focus();
        }

        if (input.length === 0 && index > 0) {
          inputRef.current[index - 1]?.focus();
        }
      }
    }
  };

  useEffect(() => {
    if (phoneNum.startsWith('+91')) {
      const newNum = phoneNum.replace('+91', '');
      setPhoneNum(newNum);
    }
  }, [phoneNum]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="sm:max-w-lg sm:p-14 p-8 top-[40vh] lg:top-[40vh] mx-auto "
        aria-describedby=""
      >
        {isLoading ? (
          <div className="flex justify-center items-center">
            <ClipLoader
              color={spinnerColor}
              loading={loading}
              size={150}
              aria-label="Loading Spinner"
              data-testid="loader"
            />
          </div>
        ) : showOTPSection ? (
          <>
            <div className="mb-2 overflow-x-hidden">
              <div className="flex gap-4 items-center">
                {/* Back Button */}
                <img
                  loading="lazy"
                  className="cursor-pointer"
                  onClick={() => setShowOTPSection(false)}
                  src="/icons/back.svg"
                  alt="back"
                />
                <p className="text-[#343434] text-left font-medium text-2xl">
                  OTP
                </p>
              </div>
              {/* Displaying Phone Number */}
              <p className="text-[#343434] text-left font-medium text-base">
                Enter OTP sent to {phoneNum}
              </p>
            </div>

            {/* OTP Input Section */}
            <div
              className={`grid grid-cols-${length} grid-flow-col lg:gap-x-3 gap-x-2 w-11/12`}
            >
              {Array.from({ length }, (_, index) => (
                <input
                  key={index}
                  type="text"
                  inputMode="numeric"
                  maxLength={1} // Ensuring only one character can be entered
                  value={OTP[index]}
                  onChange={(e) => handleTextChange(e.target.value, index)}
                  onPaste={(e) =>
                    handleTextChange(e.clipboardData.getData('Text'), index)
                  } // Handle paste
                  ref={(ref) => {
                    if (ref) {
                      inputRef.current[index] = ref as HTMLInputElement;
                    }
                  }}
                  className={`border-[#112C79] text-center w-10 h-10 md:w-[51px] md:h-[51px] rounded-sm border-[1px] bg-[#F7FAFF] outline-none`}
                />
              ))}
            </div>

            {/* Resend OTP Section */}
            <div className="mt-4">
              <span className="text-[#313131] font-normal text-sm mr-2">
                Didn’t receive OTP?
              </span>
              <span
                className={`text-blue-600 font-primary_medium text-sm cursor-pointer ${
                  resending ? 'pointer-events-none opacity-50' : ''
                }`}
                onClick={!resending ? handleResendOTP : undefined}
              >
                {!resending ? 'Resend OTP' : `Resend in ${timer}s`}
              </span>
            </div>

            {/* Verify OTP Button */}
            <div
              className="w-[90%] sm:w-[350px] mx-auto mt-2"
              onClick={() => verifyOTP(OTP.join(''))}
            >
              <SecondaryButton additionalClassNames="w-full py-4">
                {isOTPSentLoading ? (
                  <ClipLoader size={20} color="#ffffff" />
                ) : (
                  'Verify OTP'
                )}
              </SecondaryButton>
            </div>
          </>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle className="font-primary_medium text-[#181818] font-medium text-[22px] leading-7">
                Complete Profile
              </DialogTitle>
            </DialogHeader>

            <div className="mx-auto w-full">
              <label className="text-base text-[#3F3F3F]" htmlFor="full_name">
                Full Name
              </label>
              <div className="mb-4">
                <input
                  placeholder="John H"
                  id="full_name"
                  name="full_name"
                  type="text"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  className="border-none bg-[#EEEEEE] rounded-sm p-4 w-full h-[55px] mb-1"
                />
                {errors.fullName && (
                  <p className="text-red-500 text-sm">{errors.fullName}</p>
                )}
              </div>
              <label className="text-base text-[#3F3F3F]" htmlFor="email">
                Email
              </label>
              <div className="mb-4">
                <input
                  placeholder="<EMAIL>"
                  type="email"
                  id="email"
                  name="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="border-none bg-[#EEEEEE] rounded-sm p-4 w-full h-[55px] mb-1"
                />
                {errors.email && (
                  <p className="text-red-500 text-sm">{errors.email}</p>
                )}
              </div>
              <label className="text-base text-[#3F3F3F]" htmlFor="phone_num">
                Mobile Number
              </label>
              <div className="mb-4">
                <div className="flex">
                  <span className="px-2 mr-2 py-3 h-[55px] w-[55px] mb-1 border-[1px] border-[#3F3F3F] rounded-sm">
                    +91
                  </span>
                  <input
                    placeholder="Enter your phone number"
                    type="number"
                    inputMode="numeric"
                    id="phone_num"
                    name="phone_num"
                    value={phoneNum}
                    onChange={(e) => setPhoneNum(e.target.value)}
                    className="border-none bg-[#EEEEEE] rounded-sm p-4 w-full h-[55px] mb-1"
                    maxLength={10}
                  />
                </div>
                <p className="text-neutral-500 text-sm font-primary_medium">
                  Without Country Code
                </p>
                {errors.phoneNum && (
                  <p className="text-red-500 text-lg">{errors.phoneNum}</p>
                )}
              </div>
            </div>

            <DialogFooter className="sm:justify-start w-full">
              <div className="w-full">
                {/* Updated onClick handler to only call handleSubmit */}
                {!isOTPSentLoading && !isProfileUpdateLoading ? (
                  <div
                    onClick={() => handleSubmit(user.authTokens.accessToken)}
                  >
                    <SecondaryButton
                      additionalClassNames="w-full"
                      disabled={isOTPSentLoading || isProfileUpdateLoading}
                    >
                      Confirm
                    </SecondaryButton>
                  </div>
                ) : (
                  <div>
                    <SecondaryButton
                      additionalClassNames="w-full"
                      disabled={isOTPSentLoading || isProfileUpdateLoading}
                    >
                      <ClipLoader size={20} color="#ffffff" />
                    </SecondaryButton>
                  </div>
                )}
              </div>
            </DialogFooter>
            <div
              id="recaptcha"
              className="recaptcha absolute z-[9999] w-auto h-auto pointer-events-auto"
            ></div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default UserProfileStepsModal;
