import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { TestSeriesType } from "@/types/Test.ts";

interface TestSeriesState {
    selectedTestSeries : TestSeriesType | null
}


const initialState: TestSeriesState = {
    selectedTestSeries : null
};

const testSeriesSlice = createSlice({
    name : 'testseries',
    initialState,
    reducers : {
        setSelectedTestSeries : (state,action : PayloadAction<TestSeriesType | null>) => {
            state.selectedTestSeries = action.payload
        },
        clearTestSeries : (state) => {
            state.selectedTestSeries = null;
        }

    }
})


export const { setSelectedTestSeries,clearTestSeries } = testSeriesSlice.actions;
export default testSeriesSlice.reducer;