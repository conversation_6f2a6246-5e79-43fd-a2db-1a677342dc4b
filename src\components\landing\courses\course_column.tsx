'use client';

import { CourseData, CourseType } from '@/types/Course';
import CourseCard from './course_card';
import { useDispatch } from 'react-redux';
import {
  setSelectCourseType,
  selectParentCourseName,
  setSelectedMainCourse,
  setSelectedAttempt,
} from '@/redux/features/selectedFilterModalCourse';
import { openModal } from '@/redux/features/filterModalSlice';
import { FaArrowUpLong } from 'react-icons/fa6';
import { usePathname } from 'next/navigation';
import { useEffect } from 'react';

interface CourseColumnProps {
  course: CourseData;
  additionalClassNamesForCard?: string;
}

const CourseColumn = ({
  course,
  additionalClassNamesForCard,
}: CourseColumnProps) => {
  const dispatch = useDispatch();
  const pathname = usePathname();
  const isHomePage = pathname === '/' || pathname === '/dashboard';
  const isCourseRoute = pathname.startsWith('/course');
  useEffect(() => {
    // Only set default selection for non-course routes
    if (!isCourseRoute && course.course_type && course.course_type.length > 0) {
      dispatch(setSelectCourseType(course.course_type[0]));
      dispatch(selectParentCourseName(course.name));
      dispatch(setSelectedMainCourse(course));
      dispatch(setSelectedAttempt([course.course_type[0].attempts[0]]));
    }
  }, [course, dispatch, isCourseRoute]);

  const handleCardSelection = (
    parentCourseName: string,
    selectedCourseData?: CourseType
  ) => {
    dispatch(selectParentCourseName(parentCourseName));
    dispatch(setSelectedMainCourse(course));

    if (isCourseRoute && selectedCourseData) {
      // For course route, use the selected course data
      dispatch(setSelectCourseType(selectedCourseData));
      dispatch(setSelectedAttempt([selectedCourseData.attempts[0]]));
    } else {
      // For non-course route, use the first course type
      if (course.course_type && course.course_type.length > 0) {
        dispatch(setSelectCourseType(course.course_type[0]));
        dispatch(setSelectedAttempt([course.course_type[0].attempts[0]]));
      }
    }

    dispatch(openModal());
  };

  return (
    <div className=" sm:w-full lg:w-auto w-full">
      {isCourseRoute ? (
        <div className=" flex flex-col mt-4 gap-4 lg:gap-6">
          <h2 className="mt-10 text-[#1D1D1D] lg:block border-b-[1.5px] border-[#E3E3E3] font-normal leading-[46px] text-[40px]">
            {course.name}
          </h2>
          <div className="flex gap-5 mb-5 md:gap-0 lg:grid">
            {course.course_type.map((courseData, index) => (
              <div
                onClick={() => handleCardSelection(course.name, courseData)}
                key={index}
              >
                <CourseCard
                  key={index}
                  courseData={courseData}
                  additionalClassNamesForCard={additionalClassNamesForCard}
                />
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div>
          {isHomePage ? (
            ['CA', 'CMA', 'CS'].includes(course.name) && (
              <div
                className={`bg-[#EEEEEE] mr-4 p-3 min-h-[90px] md:max-w-none md:aspect-auto hover:bg-[#CFE0FF] cursor-pointer transition-all duration-150 flex  md:flex-row sm:p-4 rounded-lg shadow-md ${additionalClassNamesForCard} group`}
                onClick={() => handleCardSelection(course.name)}
              >
                <div className="w-full min-w-[60px] min-h-auto flex flex-col-reverse md:flex-row justify-between items-start md:items-center md:space-y-0 md:pt-0 md:mt-0">
                  <h2 className="text-[#1D1D1D] text-md sm:text-base md:text-[16px] font-normal leading-tight md:leading-[46px] border-b-[1.5px] border-[#E3E3E3] max-w-[75%] sm:max-w-[80%] md:max-w-[90%]">
                    {course.name}
                  </h2>
                  <div className="bg-[#FFFFFF] text-[#FFAE1E] group-hover:bg-[#2F50FF] group-hover:text-white cursor-pointer flex justify-center items-center rounded-full p-2 sm:p-3 md:px-4 ml-auto">
                    <FaArrowUpLong
                      size={12}
                      className="rotate-45 ease-in-out transition-all duration-200"
                    />
                  </div>
                </div>
              </div>
            )
          ) : (
            <div
              className={`bg-[#EEEEEE] flex mb-4 sm:mb-7 mr-4 p-3 min-h-[90px] md:max-w-none md:aspect-auto hover:bg-[#CFE0FF] cursor-pointer transition-all duration-150 md:flex-row sm:p-4 rounded-lg shadow-md ${additionalClassNamesForCard} group`}
              onClick={() => handleCardSelection(course.name)}
            >
              <div className="w-full min-w-[60px] h-auto flex flex-col-reverse md:flex-row justify-between items-start md:items-center md:space-y-0 md:pt-0 md:mt-0">
                <h2 className="text-[#1D1D1D] text-sm sm:text-base md:text-[16px] font-normal leading-tight md:leading-[46px] border-b-[1.5px] border-[#E3E3E3] max-w-[75%] sm:max-w-[80%] md:max-w-[90%]">
                  {course.name}
                </h2>
                <div className="bg-[#FFFFFF] text-[#FFAE1E] group-hover:bg-[#2F50FF] group-hover:text-white cursor-pointer flex justify-center items-center rounded-full p-2 sm:p-3 md:px-4 ml-auto">
                  <FaArrowUpLong
                    size={12}
                    className="rotate-45 ease-in-out transition-all duration-200"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CourseColumn;
