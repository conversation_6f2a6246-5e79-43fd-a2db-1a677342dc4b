import { Metadata } from 'next';
import ClientLayout from '@/components/ClientLayout';
import './globals.css';

export const metadata: Metadata = process.env.NEXT_PUBLIC_IS_PROD == "true" ? 
{
  title: "Gradehunt - India's Top Exam Preparation Platform",
  description:
    'Gradehunt is your go-to platform for CA, CS, CMA, CLAT, study abroad, and more, offering courses and test series to boost your academic and career success.',
  keywords: [
    'Gradehunt',
    'Exam Preparation',
    'Online Courses',
    'Test Series',
    'Mentorship',
    'Professional Courses',
  ],
  alternates: {canonical : 'https://gradehunt.com'},
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://gradehunt.com',
    title: "Gradehunt - India's Top Exam Preparation Platform",
    description:
      'Gradehunt is your go-to platform for CA, CS, CMA, CLAT, study abroad, and more, offering courses and test series to boost your academic and career success.',
    images: [
      {
        url: 'https://gradehunt.com/icons/gradehunt_logo.svg',
        alt: 'Gradehunt Logo',
      },
    ],
  },
  twitter: {
    card: 'summary',
    site: '@gradehunt',
    creator: '@gradehunt',
    title: "Gradehunt - India's Top Exam Preparation Platform",
    description:
      'Gradehunt is your go-to platform for CA, CS, CMA, CLAT, study abroad, and more, offering courses and test series to boost your academic and career success.',
    images: [
      {
        url: 'https://gradehunt.com/icons/gradehunt_logo.svg',
        alt: 'Gradehunt Logo',
      },
    ],
  },
} : {
  title: "Gradehunt - India's Top Exam Preparation Platform",
  description:
    'Gradehunt is your go-to platform for CA, CS, CMA, CLAT, study abroad, and more, offering courses and test series to boost your academic and career success.',
  keywords: [
    'Gradehunt',
    'Exam Preparation',
    'Online Courses',
    'Test Series',
    'Mentorship',
    'Professional Courses',
  ]
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {

  const isStaging = process.env.NEXT_PUBLIC_IS_PROD !== "true";
  
  return (
    <html lang="en">
      <head>
        <base href="/" />
        {isStaging && <meta name="robots" content="noindex, nofollow" />}
        <script
          dangerouslySetInnerHTML={{
            __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0], j=d.createElement(s), dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);})(window,document,'script','dataLayer','GTM-KF3TNG6C');`,
          }}
        />
      </head>
      <body>
        <noscript>
          <iframe
            src="https://www.googletagmanager.com/ns.html?id=GTM-KF3TNG6C"
            height="0"
            width="0"
            style={{ display: 'none', visibility: 'hidden' }}
          />
        </noscript>
        <ClientLayout>{children}</ClientLayout>
      </body>
    </html>
  );
}
