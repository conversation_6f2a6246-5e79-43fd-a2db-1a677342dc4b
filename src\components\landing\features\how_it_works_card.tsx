'use client'

import { FaChevronRight } from 'react-icons/fa';
import { useRouter } from 'next/navigation';
const HowItWorksCard = ({
  title,
  desc,
  link,
}: {
  title: string;
  desc: string;
  link: string;
}) => {
  const router = useRouter();
  const navigateClick = () => {
    router.push(link);
  };
  return (
    <div className="w-full lg:max-w-md p-6 bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300 ease-in-out">
      {/* Title */}
      <h2 className="mb-4 font-primary_medium text-[24px] leading-[30px] text-gray-900 tracking-tight">
        {title}
      </h2>

      {/* Description */}
      <p className="font-primary text-base text-gray-700 mb-4 leading-relaxed">
        {desc}
      </p>

      {/* View Button */}
      <div
        className="flex items-center cursor-pointer justify-start gap-2 text-base font-primary_medium text-blue-600 hover:text-blue-800 transition-colors duration-200 ease-in-out"
        onClick={navigateClick}
      >
        View
        <FaChevronRight size={14} />
      </div>
    </div>
  );
};

export default HowItWorksCard;
