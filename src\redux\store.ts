import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { apiSlice } from './apiSlice';
import { setupListeners } from '@reduxjs/toolkit/query';
import selectedFilterModalCourseReducer from './features/selectedFilterModalCourse';
import userSliceReducer from './features/userSlice';
import filterModalReducer from './features/filterModalSlice';
import testSeriesReducer from './features/testSeriesSlice';
import testPaperReducer from './features/testPaperSlice'
import coursePageReducer from './features/coursePageSlice'
import subjectViewReducer from './features/subjectViewSlice';
import purchasedCoursesReducer from './features/purchasedCoursesSlice';
import storage from 'redux-persist/lib/storage';
import { persistStore, persistReducer } from 'redux-persist';
import { FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from 'redux-persist';

// Combine your reducers
const rootReducer = combineReducers({
  [apiSlice.reducerPath]: apiSlice.reducer,
  course: selectedFilterModalCourseReducer,
  user: userSliceReducer,
  filterModal: filterModalReducer,
  testSeries: testSeriesReducer,
  testPaper : testPaperReducer,
  coursePage : coursePageReducer,
  subjectView: subjectViewReducer,
  purchasedCourses: purchasedCoursesReducer,
});


const persistConfig = {
  key: 'root',
  storage,
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }).concat(apiSlice.middleware),
});

// Persist store
export const persistor = persistStore(store);

setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
