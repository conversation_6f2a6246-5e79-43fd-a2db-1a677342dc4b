import React from 'react';
import { baseUrl, fallback_image } from '@/config/constants';
import dynamic from 'next/dynamic';
import FilterModal from '@/components/test_series/filter_modal';
import AuthorDisplay from '@/components/blogs/AuthorDisplay';
import SubSection from '@/components/blogs/Subsection';
import NotFoundPage from '../../not-found/page';
import { parseWithBR } from '@/lib/utils';

const PopupImageSection = dynamic(
  () => import('../../../components/blogs/popUpImage'),
  { ssr: false }
);

// ISR: Revalidate the page every 60 seconds
export const revalidate = 60;

// Generate static metadata for each page
export async function generateMetadata({
  params,
}: {
  params: { slug: string };
}) {
  // Fetch all blogs to find the correct blog by slug
  const response = await fetch(
    `${baseUrl}/z/blogs/pages/?type=blogs.BlogPage&limit=1000`,
    {
      next: { revalidate: 60 },
    }
  );
  const blogs = await response.json();
  const blog = blogs.items?.find((item: any) => item.meta.slug === params.slug);

  if (!blog) {
    return {
      title: 'Not Found - Gradehunt',
      description: 'The requested blog could not be found.',
    };
  }

  // Fetch detailed metadata for the blog
  const detailResponse = await fetch(blog.meta.detail_url, {
    next: { revalidate: 60 },
  });
  const blogDetail = await detailResponse.json();
  return {
    title: `${blogDetail.meta.seo_title}`,
    description: blogDetail.meta.search_description,
    keywords: [],
    alternates: {
      canonical: `https://gradehunt.com/blogs/${blogDetail.meta.slug}`,
    },
    openGraph: {
      type: 'website',
      locale: 'en_US',
      url: `https://gradehunt.com/blogs/${blogDetail.meta.slug}`,
      title: `${blogDetail.meta.seo_title}`,
      description: blogDetail.meta.search_description,
      images: [],
    },
    twitter: {
      card: 'summary',
      site: '@gradehunt',
      creator: '@gradehunt',
      title: `${blogDetail.meta.seo_title}`,
      description: blogDetail.meta.search_description,
      images: [],
    },
  };
}

// Blog page component
export default async function BlogPage({
  params,
}: {
  params: { slug: string };
}) {
  // Fetch all blogs (with caching for ISR)
  const response = await fetch(
    `${baseUrl}/z/blogs/pages/?type=blogs.BlogPage&limit=1000&fields=banner_image,description,category`,
    {
      next: { revalidate: 60 },
    }
  );
  const blogs = await response.json();

  // Find the blog matching the slug
  const blog = blogs.items.find((item: any) => item.meta.slug === params.slug);

  if (!blog) {
    return (
      <div>
        <NotFoundPage />
      </div>
    );
  }

  // Fetch blog details
  const detailResponse = await fetch(blog.meta.detail_url, {
    next: { revalidate: 60 },
  });
  const blogDetail = await detailResponse.json();
  // blogDetail.subsections = blogDetail.subsections.reverse()

  // Generate schema.org JSON-LD for the blog
  const schemaData = {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: blogDetail.meta.seo_title,
    description: blogDetail.meta.search_description,
    image: blogDetail.banner_image?.meta.download_url || '',
    author: blogDetail.blog_authors.map((author: any) => ({
      '@type': 'Person',
      name: author.author_name,
    })),
    publisher: {
      '@type': 'Organization',
      name: 'Gradehunt',
    },
    datePublished: blogDetail.meta.first_published_at,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `${baseUrl}/blogs/${params.slug}`,
    },
  };

  return (
    <div className="blog-parent relative overflow-hidden px-6 md:px-24 py-12 md:grid grid-cols-10 gap-6 z-[10] text-justify">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(schemaData),
        }}
      />
      <FilterModal />

      {/* Left Section */}
      <article className="md:col-span-7">
        {/* Banner Section */}
        <section className="relative mb-12">
          {blogDetail.banner_image && (
            <img
              src={blogDetail.banner_image.meta.download_url}
              alt={blogDetail.title}
              className="w-full h-auto rounded-lg shadow-lg"
            />
          )}

          <h1 className="text-4xl md:text-5xl text-left mt-8 font-bold">
            {blogDetail.title}
          </h1>
        </section>

        {/* Blog Description */}
        <section className="mb-12">
          <div className="prose prose-lg max-w-none">
            {parseWithBR(blogDetail.description)}
          </div>
        </section>

        {/* Blog Subsections */}
        <section className="mb-12">
          {blogDetail.subsections
            .slice(0, 1)
            .map((section: any, index: number) => (
              <SubSection key={index} section={section} />
            ))}
          {blogDetail.popup_image && (
            <div className="md:hidden my-12">
              <PopupImageSection
                popupImageUrl={blogDetail.popup_image.meta.download_url}
                altText={blogDetail.title}
                course_group={blogDetail.course_group}
                category={blogDetail.category}
              />
            </div>
          )}
          {blogDetail.subsections
            .slice(1)
            .map((section: any, index: number) => (
              <SubSection key={index} section={section} />
            ))}
        </section>

        {/* FAQs */}
        {blogDetail.faqs.length > 0 && (
          <section className="my-4">
            <h2 className="block font-primary_medium text-2xl md:text-3xl lg:text-4xl my-4">
              Frequently Asked Questions
            </h2>
            <div className="w-full bg-gray-50 p-6 rounded-lg shadow-md">
              {blogDetail.faqs.map((faq: any, index: number) => (
                <div
                  key={index}
                  className="mb-8 border-b border-gray-300 pb-4 last:border-none last:pb-0"
                >
                  <h3 className="flex items-start text-xl font-semibold text-gray-800 mb-2">
                    <span className="mr-2 text-gray-600">Q{index + 1}.</span>
                    <span className="flex-1">
                      {parseWithBR(faq.value.question.html)}
                    </span>
                  </h3>
                  <div className="flex items-start max-w-none text-gray-700 leading-relaxed">
                    <span className="mr-2 font-medium text-gray-500">Ans.</span>
                    <span className="flex-1">
                      {parseWithBR(faq.value.answer.html)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Related Searches */}
        {blogs.items.filter(
          (blog: any) =>
            blog.id !== blogDetail.id && blog.category == blogDetail.category
        ).length > 0 && (
          <section className="my-12">
            <h3 className="text-2xl font-extrabold text-gray-800 mb-6">
              Related Searches
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
              {blogs.items
                .filter(
                  (blog: any) =>
                    blog.id !== blogDetail.id &&
                    blog.category == blogDetail.category
                )
                .map((blog: any, index: number) => (
                  <div
                    key={index}
                    className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 ease-in-out"
                  >
                    <img
                      src={
                        blog.banner_image?.meta.download_url || fallback_image
                      }
                      alt={blog.title}
                      className="object-cover rounded-t-lg"
                    />
                    <div className="p-4">
                      <h4 className="md:text-xl font-semibold text-gray-900 mb-2">
                        {blog.title}
                      </h4>
                      <div className="text-gray-600 text-xs md:text-sm mb-4">
                        <div className="prose prose-sm max-w-none">
                          {parseWithBR(blog.description)}
                        </div>
                      </div>
                      <a
                        href={`/blogs/${blog.meta.slug}`}
                        className="text-indigo-600 hover:text-indigo-800 text-sm font-medium transition duration-200"
                      >
                        Read more
                      </a>
                    </div>
                  </div>
                ))}
            </div>
          </section>
        )}
      </article>

      {/* Right Section */}
      <section className="col-span-3 pb-8 md:pb-0 md:relative md:h-[80vh]">
        <div className="flex flex-col gap-4 md:sticky md:mx-8 z-10">
          {blogDetail.video_url && (
            <div className="rounded-lg overflow-hidden shadow-lg">
              <h2 className="text-2xl font-semibold mb-4">Watch the Video</h2>
              <div className="aspect-w-16 aspect-h-9">
                <iframe
                  src={blogDetail.video_url}
                  title="Blog Video"
                  className="w-full h-full"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                ></iframe>
              </div>
            </div>
          )}
          {blogDetail.popup_image && (
            <div className="hidden md:block -z-10">
              <PopupImageSection
                popupImageUrl={blogDetail.popup_image.meta.download_url}
                altText={blogDetail.title}
                course_group={blogDetail.course_group}
                category={blogDetail.category}
              />
            </div>
          )}
          {blogDetail.blog_authors.length > 0 && (
            <div>
              <AuthorDisplay blog_authors={blogDetail.blog_authors} />
            </div>
          )}
        </div>
      </section>
    </div>
  );
}
