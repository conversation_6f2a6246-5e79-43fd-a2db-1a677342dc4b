export interface User {
    name : string;
    userId : number | null;
    email : string;
    gender : string;
    firebaseUID : string;
    phoneNumber : string;
    emailVerified : boolean | null;
    providerApiKey : string;
    tokenResponse : {
        idToken : string;
        refreshToken : string;
        expiresIn : string;
        isNewUser : boolean | null;
    }
    authTokens : {
        accessToken : string,
        refreshToken : string,
    }
}

