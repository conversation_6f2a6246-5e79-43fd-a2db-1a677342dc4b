import { baseUrl } from '@/config/constants';
import MainSwiper from './swiper';

async function fetchTestimonials(course?: string, course_name?: string) {
  let url = `${baseUrl}/topper/?course_group_name=${course}`;

  if (course_name) {
    url += `&course_name=${course_name}`;
  }

  const res = await fetch(url, {
    method: 'GET',
    cache: 'no-store',
  });
  if (!res.ok) {
    throw new Error('Failed to fetch testimonials');
  }
  const data = await res.json();
  return data;
}

const HomePageSwiper = async ({
  course = '',
  course_name,
}: {
  course?: string;
  course_name?: string;
}) => {
  let testimonials = [];

  try {
    const data = await fetchTestimonials(course, course_name);
    testimonials = data;
  } catch (error) {
    console.error('Error fetching testimonials:', error);
  }

  if (!testimonials || testimonials.length === 0) {
    return (
      <section className=" px-[20px] py-8 lg:py-16 lg:pb-8 lg:px-[100px] lg:pt-20">
        <h2 className="block font-primary_medium text-2xl md:text-3xl lg:text-4xl my-4">
          Hear From Our Students
        </h2>
        <div className="flex flex-col items-center justify-center min-h-[200px] bg-gray-50 rounded-lg">
          <div className="text-gray-500 text-lg font-medium">
            No testimonials available yet
          </div>
          <p className="text-gray-400 text-sm mt-2">
            Check back later for updates
          </p>
        </div>
      </section>
    );
  }

  return (
    <section className=" px-[20px] py-8 lg:px-[100px]">
      <div className="relative">
        <h2 className="block font-primary_medium text-2xl md:text-3xl lg:text-4xl my-4">
          Hear From Our Students
        </h2>
        <MainSwiper allToppers={testimonials} />
      </div>
    </section>
  );
};

export default HomePageSwiper;
