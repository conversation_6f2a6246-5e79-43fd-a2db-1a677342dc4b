'use client';

import React, { useEffect, useState } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination } from 'swiper/modules';
import { fallback_image } from '@/config/constants';
import { parseWithBR } from '@/lib/utils';
import { usePathname } from 'next/navigation';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

interface BlogsCardsProps {
  category: string;
  course?: string;
}

const BlogsCards: React.FC<BlogsCardsProps> = ({ category, course = '' }) => {
  const [blogs, setBlogs] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const pathname = usePathname();
  const isBlogsPage = pathname === '/blogs';

  const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        let url = `${baseUrl}/z/blogs/pages/?type=blogs.BlogPage&limit=1000&fields=banner_image,description,category`;
        if (course) {
          url += `,course_group&course_group=${course}`;
        }

        const response = await fetch(url);

        if (!response.ok) {
          throw new Error('Failed to fetch blogs');
        }
        const data = await response.json();
        setBlogs(data.items || []);
      } catch (err: any) {
        setError(err.message || 'An error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchBlogs();
  }, [baseUrl, course]);

  if (isLoading) {
    return (
      <div className="px-[20px] py-8 lg:py-16 lg:pb-8 lg:px-[100px] lg:pt-20">
        Loading...
      </div>
    );
  }

  if (error) {
    return (
      <div className="px-[20px] py-8 lg:py-16 lg:pb-8 lg:px-[100px] lg:pt-20 text-red-600">
        Error: {error}
      </div>
    );
  }

  if (!blogs || blogs.length === 0) {
    return (
      <section className="px-[20px] py-8 lg:py-16 lg:pb-8 lg:px-[100px] lg:pt-20">
        <h3 className="block font-primary_medium text-2xl md:text-3xl lg:text-4xl my-4">
          Blogs
        </h3>
        <div className="flex flex-col items-center justify-center min-h-[200px] bg-gray-50 rounded-lg">
          <div className="text-gray-500 text-lg font-medium">
            No blogs available yet
          </div>
          <p className="text-gray-400 text-sm mt-2">
            Check back later for updates
          </p>
        </div>
      </section>
    );
  }

  // Filter blogs based on category
  const filteredBlogs = blogs.filter(
    (blog: any) => category === '' || blog.category === category
  );

  return (
    <div className="px-[20px] py-8 lg:py-16 lg:pb-8 lg:px-[100px] lg:pt-20">
      <h3 className="block font-primary_medium text-2xl md:text-3xl lg:text-4xl my-4">
        Blogs
      </h3>

      {isBlogsPage ? (
        // Regular grid layout for blogs page - show all blogs
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
          {filteredBlogs.map((blog: any) => (
            <BlogCard key={blog.id} blog={blog} />
          ))}
        </div>
      ) : (
        // Swiper for other pages
        <div className="blogs-swiper-container relative">
          {/* Desktop View - Show 9 blogs max, 3 at a time */}
          <div className="hidden md:block">
            {filteredBlogs.length > 0 && (
              <Swiper
                modules={[Navigation]}
                navigation={{
                  nextEl: '.blogs-swiper-next',
                  prevEl: '.blogs-swiper-prev',
                }}
                // pagination={{ clickable: true }}
                slidesPerView={3}
                spaceBetween={24}
                slidesPerGroup={3}
                className="blogs-swiper"
              >
                {filteredBlogs.slice(0, 9).map((blog: any) => (
                  <SwiperSlide key={`desktop-${blog.id}`}>
                    <BlogCard blog={blog} />
                  </SwiperSlide>
                ))}
              </Swiper>
            )}
          </div>

          {/* Mobile View - Show 10 blogs max, 2 at a time */}
          <div className="block md:hidden">
            <Swiper
              modules={[Navigation, Pagination]}
              navigation={{
                nextEl: '.blogs-swiper-next',
                prevEl: '.blogs-swiper-prev',
              }}
              // pagination={{ clickable: true }}
              slidesPerView={1}
              spaceBetween={16}
              className="blogs-swiper"
            >
              {filteredBlogs.slice(0, 10).map((blog: any) => (
                <SwiperSlide key={`mobile-${blog.id}`}>
                  <BlogCard blog={blog} />
                </SwiperSlide>
              ))}
            </Swiper>
          </div>

          <div className="blogs-swiper-prev absolute top-1/2 left-0 z-10 -translate-y-1/2 cursor-pointer"></div>
          <div className="blogs-swiper-next absolute top-1/2 right-0 z-10 -translate-y-1/2 cursor-pointer"></div>
        </div>
      )}
    </div>
  );
};

const BlogCard = ({ blog }: { blog: any }) => {
  return (
    <div className="bg-white w-full h-[600px] rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 ease-in-out flex flex-col relative">
      {/* Image container with fixed aspect ratio */}
      <div className="w-full aspect-video">
        <img
          src={blog.banner_image?.meta.download_url || fallback_image}
          alt={blog.title || 'Blog Banner'}
          className="w-full object-contain max-h-[350px] bg-gray-100"
        />
      </div>

      {/* Content container */}
      <div className="p-4 flex-grow">
        {/* Title */}
        <h4 className="text-lg font-semibold text-gray-900 line-clamp-2 mb-3">
          {blog.title}
        </h4>

        {/* Description with padding bottom to make space for the read more link */}
        <div className="overflow-hidden pb-12">
          <p className="text-gray-600 text-sm leading-relaxed line-clamp-5">
            {parseWithBR(blog.description)}
          </p>
        </div>
      </div>

      {/* Read more link positioned absolutely at the bottom */}
      <div className="absolute bottom-4 left-4 right-4">
        <a
          href={`blogs/${blog.meta.slug}`}
          className="text-indigo-600 hover:text-indigo-800 text-sm font-medium transition duration-200 block"
        >
          Read more
        </a>
      </div>
    </div>
  );
};

export default BlogsCards;
