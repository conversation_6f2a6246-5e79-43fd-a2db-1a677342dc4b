import {
  openModal,
  openModalWithBoards,
  openModalWithProfessional,
} from '@/redux/features/filterModalSlice';
import { FaChevronRight } from 'react-icons/fa';
import { FaArrowRight } from 'react-icons/fa6';
import { useDispatch } from 'react-redux';

interface TestSeriesCardProps {
  title: string;
  description: string;
  courseType?: {
    course_group_name?: string;
    title?: string;
  };
}

const LandingTestSeriesCard = ({
  title,
  description,
  courseType,
}: TestSeriesCardProps) => {
  const dispatch = useDispatch();

  const handleOpenModal = () => {
    if (courseType) {
      if (courseType.course_group_name === 'boards') {
        dispatch(openModalWithBoards(courseType.title));
      } else if (courseType.course_group_name === 'professional') {
        dispatch(openModalWithProfessional(courseType.title));
      } else {
        dispatch(openModal());
      }
    } else {
      dispatch(openModal());
    }
  };

  const descriptionLines = description.split(',');

  return (
    <div className="bg-[#fac20a] rounded-xl shadow-lg flex flex-col p-4 hover:shadow-xl transition-shadow duration-300 ease-in-out mx-2 h-full">
      {/* Heading section */}
      <h2 className="text-gray-800 font-bold text-xl">{title}</h2>

      {/* Description section without height restriction */}
      <div className="p-1 rounded-lg text-gray-800 my-4 font-normal text-[12px] space-y-3 flex-grow">
        {descriptionLines.map((line, index) => (
          <div key={index} className="grid grid-cols-10 items-start space-x-2">
            <FaArrowRight className="text-gray-800" size={16} />
            <p className="col-span-9">{line.trim()}</p>
          </div>
        ))}
      </div>

      <div className="mt-auto">
        <button
          className="text-white bg-gray-800 rounded-full py-2 px-6 font-medium text-base flex items-center justify-center hover:bg-black transition-colors duration-200 ease-in-out w-max"
          onClick={handleOpenModal}
        >
          Buy Now
          <FaChevronRight className="ml-2" color="white" size={14} />
        </button>
      </div>
    </div>
  );
};

export default LandingTestSeriesCard;
