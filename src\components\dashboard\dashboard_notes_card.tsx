import { FaChevronRight } from "react-icons/fa"

const DashboardNotesCard = ({ subjectTitle, courseTypeTitle, notesURL }: { subjectTitle: string, courseTypeTitle: string, notesURL: string }) => {
    return (
        <div className='bg-[#EEEEEE] w-full rounded-lg p-4 flex justify-between items-center lg:h-auto'>
            <div className="w-4/5 md:w-2/5">
                <p className="text-[#000000] text-base leading-5 font-normal">
                    {subjectTitle
                        .toLowerCase()
                        .replace(/\b\w/g, (char) => char.toUpperCase())
                    }
                </p>
                <p className="text-[#49454F] text-[12px] lg:text-sm leading-5 font-normal">{courseTypeTitle}</p>
            </div>
            <a target="_blank" href={notesURL} className="bg-[#FCFCFC] text-[#FFAE1E] hover:bg-[#2F50FF] transition-all duration-200 ease-in-out hover:text-[white] cursor-pointer flex justify-center items-center rounded-full p-3 ">
                <FaChevronRight size={12} />
            </a>
        </div>
    )
}

export default DashboardNotesCard