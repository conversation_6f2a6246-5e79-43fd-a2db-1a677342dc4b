'use client';

import ReduxProvider from '@/app/provider';
import ScrollToTop from '@/components/ScrollToTop';
import Navbar from '@/components/navbar/Navbar';
import Footer from './footer/footer';
import { usePathname } from 'next/navigation';

const ClientLayout = ({ children }: { children: React.ReactNode }) => {
  const pathname = usePathname(); 
  
  return (
    <ReduxProvider>
      <div id="root" className="font-primary">
        <ScrollToTop />
        {pathname !== '/auth/login' && <Navbar />}
        <main className="min-h-screen">{children}</main>
        <Footer />
      </div>
    </ReduxProvider>
  );
};

export default ClientLayout;
