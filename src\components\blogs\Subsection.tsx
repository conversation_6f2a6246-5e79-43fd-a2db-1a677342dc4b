import { parseWithBR } from '@/lib/utils';
import React from 'react';

interface ColumnDefinition {
  type: string;
  heading: string;
}

interface CellContent {
  html: string;
  images: any[];
}

interface TableData {
  columns: ColumnDefinition[];
  rows: CellContent[][];
  caption?: string;
}

interface ImageData {
  meta: {
    download_url: string;
  };
  title: string;
}

interface SectionValue {
  subheading: {
    html: string;
  };
  text_content: {
    html: string;
  };
  table?: TableData;
  image?: ImageData;
}

interface Section {
  id: string | number;
  value: SectionValue;
}

interface SubSectionProps {
  section: Section;
}

const SubSection: React.FC<SubSectionProps> = ({ section }) => {
  return (
    <div key={section.id} className="mb-12">
      {/* Subheading */}
      <h2 className="text-2xl font-semibold mb-4">
        {parseWithBR(section.value.subheading.html)}
      </h2>

      {/* Text Content */}
      <div className="prose max-w-none mb-6">
        {parseWithBR(section.value.text_content.html)}
      </div>

      {/* Table */}
      {section.value.table && (
        <div className="overflow-x-auto mb-6">
          <table className="min-w-full border-collapse border border-gray-200 rounded-lg shadow-sm">
            {/* Table Header */}
            <thead className="bg-gray-100">
              <tr>
                {section.value.table.columns.map((column, index) => (
                  <th
                    key={index}
                    className="border border-gray-300 px-4 py-2 text-left text-sm font-medium text-gray-700"
                  >
                    {column.heading}
                  </th>
                ))}
              </tr>
            </thead>

            {/* Table Body */}
            <tbody>
              {section.value.table.rows.map((row, rowIndex) => (
                <tr key={rowIndex} className="odd:bg-white even:bg-gray-50">
                  {row.map((cell, cellIndex) => (
                    <td
                      key={cellIndex}
                      className="border border-gray-300 px-4 py-2 text-sm text-gray-700"
                    >
                      {parseWithBR(cell.html)}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
          {section.value.table.caption && (
            <div className="mt-2 text-sm text-gray-500">
              {section.value.table.caption}
            </div>
          )}
        </div>
      )}

      {/* Image */}
      {section.value.image && (
        <img
          src={section.value.image.meta.download_url}
          alt={section.value.image.title}
          className="w-full h-auto rounded-lg shadow-md"
        />
      )}
    </div>
  );
};

export default SubSection;
