type Props = {
    cardNum : number
    title : string
    desc : string
}

const OurUniqueFeatureCard = ({cardNum,title,desc}: Props) => {
  return (
    <div className='bg-[#EEEEEE] p-4 gap-4 flex flex-col w-full rounded-lg'>
        <div className='bg-[#FFAE1E] flex justify-center p-1 text-[#FCFCFC] font-normal text-base items-center rounded-full w-[40px] h-[40px]'>
            {cardNum}
        </div>
        <div className='mt-auto'>
            <h2 className='text-[#1D1D1D] font-primary_medium text-base'>
                {title}
            </h2>
            <p className='text-[#3F3F3F] text-base font-normal'>
                {desc}
            </p>
        </div>

    </div>
  )
}

export default OurUniqueFeatureCard