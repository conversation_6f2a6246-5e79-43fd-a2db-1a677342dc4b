// hooks/useUserData.ts
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useGetUserDetailsQuery } from '@/redux/apiSlice';
// import { updateUser } from '@/redux/features/userSlice';
import { RootState } from '@/redux/store';

export const useUserData = () => {
  const dispatch = useDispatch();
  const user = useSelector((state : RootState) => state.user);
  const { data: userData, isLoading } = useGetUserDetailsQuery(
    { headerToken: user.tokenResponse.idToken, userId: String(user.userId) },
    { skip: !user.tokenResponse.idToken }
  );

  useEffect(() => {
    // if (userData) {
    //   dispatch(updateUser({
    //     gender: userData.gender,
    //     email: userData.email,
    //     name: userData.name,
    //     firebaseUID: userData.firebase_uid ?? '',
    //   }));
    // }
  }, [userData, dispatch]);

  return { userData, isLoading };
};